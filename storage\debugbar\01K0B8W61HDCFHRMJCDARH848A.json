{"__meta": {"id": "01K0B8W61HDCFHRMJCDARH848A", "datetime": "2025-07-17 11:04:27", "utime": **********.826619, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 57, "messages": [{"message": "[11:04:27] LOG.info: === REORDER MENU ITEM CALLED ===", "message_html": null, "is_string": false, "label": "info", "time": **********.413025, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Items received: [\n    {\n        \"id\": 17,\n        \"order\": 0,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 18,\n        \"order\": 1,\n        \"parent_id\": null,\n        \"children\": [\n            {\n                \"id\": 19,\n                \"order\": 0,\n                \"parent_id\": 18,\n                \"children\": [\n                    {\n                        \"id\": 20,\n                        \"order\": 0,\n                        \"parent_id\": 19\n                    },\n                    {\n                        \"id\": 21,\n                        \"order\": 1,\n                        \"parent_id\": 19\n                    },\n                    {\n                        \"id\": 22,\n                        \"order\": 2,\n                        \"parent_id\": 19\n                    },\n                    {\n                        \"id\": 23,\n                        \"order\": 3,\n                        \"parent_id\": 19\n                    }\n                ]\n            },\n            {\n                \"id\": 24,\n                \"order\": 1,\n                \"parent_id\": 18\n            },\n            {\n                \"id\": 25,\n                \"order\": 2,\n                \"parent_id\": 18\n            },\n            {\n                \"id\": 26,\n                \"order\": 3,\n                \"parent_id\": 18\n            },\n            {\n                \"id\": 27,\n                \"order\": 4,\n                \"parent_id\": 18\n            }\n        ]\n    },\n    {\n        \"id\": 28,\n        \"order\": 2,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 29,\n        \"order\": 3,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 30,\n        \"order\": 4,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 31,\n        \"order\": 5,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 32,\n        \"order\": 6,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 65,\n        \"order\": 7,\n        \"parent_id\": null\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.413302, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: === UPDATE MENU STRUCTURE ===", "message_html": null, "is_string": false, "label": "info", "time": **********.413411, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Items to process: [\n    {\n        \"id\": 17,\n        \"order\": 0,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 18,\n        \"order\": 1,\n        \"parent_id\": null,\n        \"children\": [\n            {\n                \"id\": 19,\n                \"order\": 0,\n                \"parent_id\": 18,\n                \"children\": [\n                    {\n                        \"id\": 20,\n                        \"order\": 0,\n                        \"parent_id\": 19\n                    },\n                    {\n                        \"id\": 21,\n                        \"order\": 1,\n                        \"parent_id\": 19\n                    },\n                    {\n                        \"id\": 22,\n                        \"order\": 2,\n                        \"parent_id\": 19\n                    },\n                    {\n                        \"id\": 23,\n                        \"order\": 3,\n                        \"parent_id\": 19\n                    }\n                ]\n            },\n            {\n                \"id\": 24,\n                \"order\": 1,\n                \"parent_id\": 18\n            },\n            {\n                \"id\": 25,\n                \"order\": 2,\n                \"parent_id\": 18\n            },\n            {\n                \"id\": 26,\n                \"order\": 3,\n                \"parent_id\": 18\n            },\n            {\n                \"id\": 27,\n                \"order\": 4,\n                \"parent_id\": 18\n            }\n        ]\n    },\n    {\n        \"id\": 28,\n        \"order\": 2,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 29,\n        \"order\": 3,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 30,\n        \"order\": 4,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 31,\n        \"order\": 5,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 32,\n        \"order\": 6,\n        \"parent_id\": null\n    },\n    {\n        \"id\": 65,\n        \"order\": 7,\n        \"parent_id\": null\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.413533, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: === PROCESS MENU ITEMS ===", "message_html": null, "is_string": false, "label": "info", "time": **********.417038, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Parent ID: {\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.417164, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Items count: {\n    \"count\": 8\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.417264, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 0: {\n    \"id\": 17,\n    \"order\": 0,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.417369, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 17,\n    \"title\": \"V\\u1ec1 ch\\u00fang t\\u00f4i\",\n    \"order\": 0,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.421725, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 1: {\n    \"id\": 18,\n    \"order\": 1,\n    \"parent_id\": null,\n    \"children\": [\n        {\n            \"id\": 19,\n            \"order\": 0,\n            \"parent_id\": 18,\n            \"children\": [\n                {\n                    \"id\": 20,\n                    \"order\": 0,\n                    \"parent_id\": 19\n                },\n                {\n                    \"id\": 21,\n                    \"order\": 1,\n                    \"parent_id\": 19\n                },\n                {\n                    \"id\": 22,\n                    \"order\": 2,\n                    \"parent_id\": 19\n                },\n                {\n                    \"id\": 23,\n                    \"order\": 3,\n                    \"parent_id\": 19\n                }\n            ]\n        },\n        {\n            \"id\": 24,\n            \"order\": 1,\n            \"parent_id\": 18\n        },\n        {\n            \"id\": 25,\n            \"order\": 2,\n            \"parent_id\": 18\n        },\n        {\n            \"id\": 26,\n            \"order\": 3,\n            \"parent_id\": 18\n        },\n        {\n            \"id\": 27,\n            \"order\": 4,\n            \"parent_id\": 18\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.42186, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 18,\n    \"title\": \"S\\u1ea3n ph\\u1ea9m\",\n    \"order\": 1,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.423517, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing children for item 18: [\n    {\n        \"id\": 19,\n        \"order\": 0,\n        \"parent_id\": 18,\n        \"children\": [\n            {\n                \"id\": 20,\n                \"order\": 0,\n                \"parent_id\": 19\n            },\n            {\n                \"id\": 21,\n                \"order\": 1,\n                \"parent_id\": 19\n            },\n            {\n                \"id\": 22,\n                \"order\": 2,\n                \"parent_id\": 19\n            },\n            {\n                \"id\": 23,\n                \"order\": 3,\n                \"parent_id\": 19\n            }\n        ]\n    },\n    {\n        \"id\": 24,\n        \"order\": 1,\n        \"parent_id\": 18\n    },\n    {\n        \"id\": 25,\n        \"order\": 2,\n        \"parent_id\": 18\n    },\n    {\n        \"id\": 26,\n        \"order\": 3,\n        \"parent_id\": 18\n    },\n    {\n        \"id\": 27,\n        \"order\": 4,\n        \"parent_id\": 18\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.423673, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: === PROCESS MENU ITEMS ===", "message_html": null, "is_string": false, "label": "info", "time": **********.423783, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Parent ID: {\n    \"parent_id\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.423889, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Items count: {\n    \"count\": 5\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.424002, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 0: {\n    \"id\": 19,\n    \"order\": 0,\n    \"parent_id\": 18,\n    \"children\": [\n        {\n            \"id\": 20,\n            \"order\": 0,\n            \"parent_id\": 19\n        },\n        {\n            \"id\": 21,\n            \"order\": 1,\n            \"parent_id\": 19\n        },\n        {\n            \"id\": 22,\n            \"order\": 2,\n            \"parent_id\": 19\n        },\n        {\n            \"id\": 23,\n            \"order\": 3,\n            \"parent_id\": 19\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.424111, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 19,\n    \"title\": \"Thi\\u1ebft b\\u1ecb \\u00e2m thanh\",\n    \"order\": 0,\n    \"parent_id\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.42623, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing children for item 19: [\n    {\n        \"id\": 20,\n        \"order\": 0,\n        \"parent_id\": 19\n    },\n    {\n        \"id\": 21,\n        \"order\": 1,\n        \"parent_id\": 19\n    },\n    {\n        \"id\": 22,\n        \"order\": 2,\n        \"parent_id\": 19\n    },\n    {\n        \"id\": 23,\n        \"order\": 3,\n        \"parent_id\": 19\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.426378, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: === PROCESS MENU ITEMS ===", "message_html": null, "is_string": false, "label": "info", "time": **********.426489, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Parent ID: {\n    \"parent_id\": 19\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.426599, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Items count: {\n    \"count\": 4\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.426695, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 0: {\n    \"id\": 20,\n    \"order\": 0,\n    \"parent_id\": 19\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.426795, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 20,\n    \"title\": \"Loa\",\n    \"order\": 0,\n    \"parent_id\": 19\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.428246, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 1: {\n    \"id\": 21,\n    \"order\": 1,\n    \"parent_id\": 19\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.428354, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 21,\n    \"title\": \"Micro\",\n    \"order\": 1,\n    \"parent_id\": 19\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.429601, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 2: {\n    \"id\": 22,\n    \"order\": 2,\n    \"parent_id\": 19\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.429756, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 22,\n    \"title\": \"Amplifier\",\n    \"order\": 2,\n    \"parent_id\": 19\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.431028, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 3: {\n    \"id\": 23,\n    \"order\": 3,\n    \"parent_id\": 19\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.431293, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 23,\n    \"title\": \"<PERSON> nghe\",\n    \"order\": 3,\n    \"parent_id\": 19\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.432927, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 1: {\n    \"id\": 24,\n    \"order\": 1,\n    \"parent_id\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.433038, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 24,\n    \"title\": \"Thi\\u1ebft b\\u1ecb \\u00e1nh s\\u00e1ng\",\n    \"order\": 1,\n    \"parent_id\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.434422, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 2: {\n    \"id\": 25,\n    \"order\": 2,\n    \"parent_id\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.434529, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 25,\n    \"title\": \"Thi\\u1ebft b\\u1ecb h\\u1ed9i ngh\\u1ecb\",\n    \"order\": 2,\n    \"parent_id\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.435911, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 3: {\n    \"id\": 26,\n    \"order\": 3,\n    \"parent_id\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.436017, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 26,\n    \"title\": \"Thi\\u1ebft b\\u1ecb truy\\u1ec1n h\\u00ecnh\",\n    \"order\": 3,\n    \"parent_id\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.437293, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 4: {\n    \"id\": 27,\n    \"order\": 4,\n    \"parent_id\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.4374, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 27,\n    \"title\": \"Ph\\u1ee5 ki\\u1ec7n\",\n    \"order\": 4,\n    \"parent_id\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.438643, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 2: {\n    \"id\": 28,\n    \"order\": 2,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.438754, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 28,\n    \"title\": \"Gi\\u1ea3i ph\\u00e1p\",\n    \"order\": 2,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.439984, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 3: {\n    \"id\": 29,\n    \"order\": 3,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.440091, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 29,\n    \"title\": \"Th\\u01b0\\u01a1ng hi\\u1ec7u\",\n    \"order\": 3,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.441311, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 4: {\n    \"id\": 30,\n    \"order\": 4,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.441418, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 30,\n    \"title\": \"D\\u1ef1 \\u00e1n\",\n    \"order\": 4,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.442687, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 5: {\n    \"id\": 31,\n    \"order\": 5,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.442795, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 31,\n    \"title\": \"Tin t\\u1ee9c\",\n    \"order\": 5,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.444013, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 6: {\n    \"id\": 32,\n    \"order\": 6,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.44412, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 32,\n    \"title\": \"Li\\u00ean h\\u1ec7\",\n    \"order\": 6,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.459584, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Processing item 7: {\n    \"id\": 65,\n    \"order\": 7,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.459703, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu item saved: {\n    \"id\": 65,\n    \"title\": \"5 xu h\\u01b0\\u1edbng c\\u00f4ng ngh\\u1ec7 \\u00e2m thanh s\\u1ebd th\\u1ed1ng tr\\u1ecb n\\u0103m 2024\",\n    \"order\": 7,\n    \"parent_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.462918, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu cache cleared for menu: <PERSON><PERSON> (VI) {\n    \"menu_id\": 5,\n    \"menu_slug\": \"main-menu-vi\",\n    \"menu_location\": \"main-menu\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.514506, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.517519, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.572952, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.602725, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.63384, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.652755, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.668173, "xdebug_link": null, "collector": "log"}, {"message": "[11:04:27] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.68102, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752725066.821243, "end": **********.826706, "duration": 1.005462884902954, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": 1752725066.821243, "relative_start": 0, "end": **********.133417, "relative_end": **********.133417, "duration": 0.*****************, "duration_str": "312ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.133429, "relative_start": 0.*****************, "end": **********.826708, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "693ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.292759, "relative_start": 0.****************, "end": **********.294953, "relative_end": **********.294953, "duration": 0.0021941661834716797, "duration_str": "2.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.admin.resources.menu-item-resource.pages.manage-menu-items", "start": **********.558518, "relative_start": 0.****************, "end": **********.558518, "relative_end": **********.558518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.562728, "relative_start": 0.****************, "end": **********.562728, "relative_end": **********.562728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.563775, "relative_start": 0.7425320148468018, "end": **********.563775, "relative_end": **********.563775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.565814, "relative_start": 0.7445709705352783, "end": **********.565814, "relative_end": **********.565814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.566118, "relative_start": 0.7448749542236328, "end": **********.566118, "relative_end": **********.566118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.566539, "relative_start": 0.7452960014343262, "end": **********.566539, "relative_end": **********.566539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.567742, "relative_start": 0.7464990615844727, "end": **********.567742, "relative_end": **********.567742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.568676, "relative_start": 0.7474329471588135, "end": **********.568676, "relative_end": **********.568676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.569125, "relative_start": 0.7478818893432617, "end": **********.569125, "relative_end": **********.569125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.570292, "relative_start": 0.7490489482879639, "end": **********.570292, "relative_end": **********.570292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.57073, "relative_start": 0.7494869232177734, "end": **********.57073, "relative_end": **********.57073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.57115, "relative_start": 0.7499070167541504, "end": **********.57115, "relative_end": **********.57115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.571423, "relative_start": 0.7501800060272217, "end": **********.571423, "relative_end": **********.571423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.571798, "relative_start": 0.7505550384521484, "end": **********.571798, "relative_end": **********.571798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.572363, "relative_start": 0.75111985206604, "end": **********.572363, "relative_end": **********.572363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.599696, "relative_start": 0.7784528732299805, "end": **********.599696, "relative_end": **********.599696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.600198, "relative_start": 0.7789549827575684, "end": **********.600198, "relative_end": **********.600198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.601458, "relative_start": 0.7802150249481201, "end": **********.601458, "relative_end": **********.601458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.601673, "relative_start": 0.7804298400878906, "end": **********.601673, "relative_end": **********.601673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.602076, "relative_start": 0.7808330059051514, "end": **********.602076, "relative_end": **********.602076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.602326, "relative_start": 0.7810828685760498, "end": **********.602326, "relative_end": **********.602326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.628908, "relative_start": 0.8076648712158203, "end": **********.628908, "relative_end": **********.628908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.629308, "relative_start": 0.8080649375915527, "end": **********.629308, "relative_end": **********.629308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.630553, "relative_start": 0.8093099594116211, "end": **********.630553, "relative_end": **********.630553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.63076, "relative_start": 0.8095169067382812, "end": **********.63076, "relative_end": **********.63076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.631171, "relative_start": 0.8099279403686523, "end": **********.631171, "relative_end": **********.631171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.631426, "relative_start": 0.810183048248291, "end": **********.631426, "relative_end": **********.631426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.631656, "relative_start": 0.8104128837585449, "end": **********.631656, "relative_end": **********.631656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.632823, "relative_start": 0.8115799427032471, "end": **********.632823, "relative_end": **********.632823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.633027, "relative_start": 0.811784029006958, "end": **********.633027, "relative_end": **********.633027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.63343, "relative_start": 0.8121869564056396, "end": **********.63343, "relative_end": **********.63343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.650047, "relative_start": 0.8288040161132812, "end": **********.650047, "relative_end": **********.650047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.65168, "relative_start": 0.8304369449615479, "end": **********.65168, "relative_end": **********.65168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.651922, "relative_start": 0.8306789398193359, "end": **********.651922, "relative_end": **********.651922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.65234, "relative_start": 0.831096887588501, "end": **********.65234, "relative_end": **********.65234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.665491, "relative_start": 0.8442480564117432, "end": **********.665491, "relative_end": **********.665491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.66704, "relative_start": 0.845797061920166, "end": **********.66704, "relative_end": **********.66704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.667322, "relative_start": 0.8460788726806641, "end": **********.667322, "relative_end": **********.667322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.667757, "relative_start": 0.8465139865875244, "end": **********.667757, "relative_end": **********.667757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.68048, "relative_start": 0.8592369556427002, "end": **********.68048, "relative_end": **********.68048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.693708, "relative_start": 0.8724648952484131, "end": **********.693708, "relative_end": **********.693708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.694622, "relative_start": 0.8733789920806885, "end": **********.694622, "relative_end": **********.694622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.695094, "relative_start": 0.8738510608673096, "end": **********.695094, "relative_end": **********.695094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.695466, "relative_start": 0.874222993850708, "end": **********.695466, "relative_end": **********.695466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.69574, "relative_start": 0.8744969367980957, "end": **********.69574, "relative_end": **********.69574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.697133, "relative_start": 0.8758900165557861, "end": **********.697133, "relative_end": **********.697133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.69738, "relative_start": 0.8761370182037354, "end": **********.69738, "relative_end": **********.69738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.698845, "relative_start": 0.8776018619537354, "end": **********.698845, "relative_end": **********.698845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.699321, "relative_start": 0.8780779838562012, "end": **********.699321, "relative_end": **********.699321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.700636, "relative_start": 0.8793928623199463, "end": **********.700636, "relative_end": **********.700636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.700998, "relative_start": 0.8797550201416016, "end": **********.700998, "relative_end": **********.700998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.701755, "relative_start": 0.880511999130249, "end": **********.701755, "relative_end": **********.701755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.702089, "relative_start": 0.8808460235595703, "end": **********.702089, "relative_end": **********.702089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.702356, "relative_start": 0.8811130523681641, "end": **********.702356, "relative_end": **********.702356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.702625, "relative_start": 0.8813819885253906, "end": **********.702625, "relative_end": **********.702625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.703855, "relative_start": 0.8826119899749756, "end": **********.703855, "relative_end": **********.703855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.704077, "relative_start": 0.8828339576721191, "end": **********.704077, "relative_end": **********.704077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.705218, "relative_start": 0.8839750289916992, "end": **********.705218, "relative_end": **********.705218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.705511, "relative_start": 0.884268045425415, "end": **********.705511, "relative_end": **********.705511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.706685, "relative_start": 0.8854420185089111, "end": **********.706685, "relative_end": **********.706685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.706936, "relative_start": 0.885692834854126, "end": **********.706936, "relative_end": **********.706936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.707658, "relative_start": 0.8864150047302246, "end": **********.707658, "relative_end": **********.707658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.707974, "relative_start": 0.8867309093475342, "end": **********.707974, "relative_end": **********.707974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.708226, "relative_start": 0.8869829177856445, "end": **********.708226, "relative_end": **********.708226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.708493, "relative_start": 0.8872499465942383, "end": **********.708493, "relative_end": **********.708493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.709709, "relative_start": 0.8884658813476562, "end": **********.709709, "relative_end": **********.709709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.709923, "relative_start": 0.8886799812316895, "end": **********.709923, "relative_end": **********.709923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.711048, "relative_start": 0.8898048400878906, "end": **********.711048, "relative_end": **********.711048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.711336, "relative_start": 0.8900928497314453, "end": **********.711336, "relative_end": **********.711336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.712523, "relative_start": 0.891279935836792, "end": **********.712523, "relative_end": **********.712523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.712767, "relative_start": 0.8915238380432129, "end": **********.712767, "relative_end": **********.712767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.713767, "relative_start": 0.892524003982544, "end": **********.713767, "relative_end": **********.713767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.714221, "relative_start": 0.8929779529571533, "end": **********.714221, "relative_end": **********.714221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.714476, "relative_start": 0.893233060836792, "end": **********.714476, "relative_end": **********.714476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.714728, "relative_start": 0.8934850692749023, "end": **********.714728, "relative_end": **********.714728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.715942, "relative_start": 0.8946988582611084, "end": **********.715942, "relative_end": **********.715942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.716161, "relative_start": 0.8949179649353027, "end": **********.716161, "relative_end": **********.716161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.717326, "relative_start": 0.896082878112793, "end": **********.717326, "relative_end": **********.717326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.71762, "relative_start": 0.8963768482208252, "end": **********.71762, "relative_end": **********.71762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.718764, "relative_start": 0.8975210189819336, "end": **********.718764, "relative_end": **********.718764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.719021, "relative_start": 0.8977780342102051, "end": **********.719021, "relative_end": **********.719021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.719749, "relative_start": 0.8985059261322021, "end": **********.719749, "relative_end": **********.719749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.720055, "relative_start": 0.8988120555877686, "end": **********.720055, "relative_end": **********.720055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.720306, "relative_start": 0.8990628719329834, "end": **********.720306, "relative_end": **********.720306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.720562, "relative_start": 0.8993189334869385, "end": **********.720562, "relative_end": **********.720562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.721766, "relative_start": 0.9005229473114014, "end": **********.721766, "relative_end": **********.721766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.721983, "relative_start": 0.9007399082183838, "end": **********.721983, "relative_end": **********.721983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.723101, "relative_start": 0.901857852935791, "end": **********.723101, "relative_end": **********.723101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.723379, "relative_start": 0.9021358489990234, "end": **********.723379, "relative_end": **********.723379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.724517, "relative_start": 0.9032740592956543, "end": **********.724517, "relative_end": **********.724517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.724764, "relative_start": 0.9035210609436035, "end": **********.724764, "relative_end": **********.724764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.725477, "relative_start": 0.9042339324951172, "end": **********.725477, "relative_end": **********.725477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.725782, "relative_start": 0.9045388698577881, "end": **********.725782, "relative_end": **********.725782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.726034, "relative_start": 0.9047908782958984, "end": **********.726034, "relative_end": **********.726034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.726305, "relative_start": 0.9050619602203369, "end": **********.726305, "relative_end": **********.726305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.727478, "relative_start": 0.9062349796295166, "end": **********.727478, "relative_end": **********.727478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.72769, "relative_start": 0.9064469337463379, "end": **********.72769, "relative_end": **********.72769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.728932, "relative_start": 0.9076888561248779, "end": **********.728932, "relative_end": **********.728932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.729336, "relative_start": 0.9080929756164551, "end": **********.729336, "relative_end": **********.729336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.730529, "relative_start": 0.9092860221862793, "end": **********.730529, "relative_end": **********.730529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.730769, "relative_start": 0.9095258712768555, "end": **********.730769, "relative_end": **********.730769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.731474, "relative_start": 0.9102308750152588, "end": **********.731474, "relative_end": **********.731474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.73178, "relative_start": 0.9105370044708252, "end": **********.73178, "relative_end": **********.73178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.732029, "relative_start": 0.9107859134674072, "end": **********.732029, "relative_end": **********.732029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.732295, "relative_start": 0.9110519886016846, "end": **********.732295, "relative_end": **********.732295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.733463, "relative_start": 0.9122200012207031, "end": **********.733463, "relative_end": **********.733463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.733667, "relative_start": 0.912423849105835, "end": **********.733667, "relative_end": **********.733667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.734793, "relative_start": 0.9135499000549316, "end": **********.734793, "relative_end": **********.734793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.735075, "relative_start": 0.9138319492340088, "end": **********.735075, "relative_end": **********.735075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.736209, "relative_start": 0.9149658679962158, "end": **********.736209, "relative_end": **********.736209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.736485, "relative_start": 0.9152419567108154, "end": **********.736485, "relative_end": **********.736485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.737209, "relative_start": 0.9159660339355469, "end": **********.737209, "relative_end": **********.737209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.737515, "relative_start": 0.9162719249725342, "end": **********.737515, "relative_end": **********.737515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.737753, "relative_start": 0.9165098667144775, "end": **********.737753, "relative_end": **********.737753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.738005, "relative_start": 0.9167618751525879, "end": **********.738005, "relative_end": **********.738005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.739197, "relative_start": 0.9179539680480957, "end": **********.739197, "relative_end": **********.739197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.739406, "relative_start": 0.9181630611419678, "end": **********.739406, "relative_end": **********.739406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.740523, "relative_start": 0.9192800521850586, "end": **********.740523, "relative_end": **********.740523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.740804, "relative_start": 0.9195609092712402, "end": **********.740804, "relative_end": **********.740804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.741935, "relative_start": 0.920691967010498, "end": **********.741935, "relative_end": **********.741935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.742202, "relative_start": 0.9209589958190918, "end": **********.742202, "relative_end": **********.742202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.742916, "relative_start": 0.921673059463501, "end": **********.742916, "relative_end": **********.742916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.743233, "relative_start": 0.921989917755127, "end": **********.743233, "relative_end": **********.743233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.743482, "relative_start": 0.9222390651702881, "end": **********.743482, "relative_end": **********.743482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.743737, "relative_start": 0.9224939346313477, "end": **********.743737, "relative_end": **********.743737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.74523, "relative_start": 0.9239869117736816, "end": **********.74523, "relative_end": **********.74523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.745475, "relative_start": 0.924232006072998, "end": **********.745475, "relative_end": **********.745475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.746612, "relative_start": 0.9253690242767334, "end": **********.746612, "relative_end": **********.746612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.746901, "relative_start": 0.9256579875946045, "end": **********.746901, "relative_end": **********.746901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.748065, "relative_start": 0.9268219470977783, "end": **********.748065, "relative_end": **********.748065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.748342, "relative_start": 0.9270989894866943, "end": **********.748342, "relative_end": **********.748342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.749063, "relative_start": 0.9278199672698975, "end": **********.749063, "relative_end": **********.749063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.749377, "relative_start": 0.9281339645385742, "end": **********.749377, "relative_end": **********.749377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.749622, "relative_start": 0.9283790588378906, "end": **********.749622, "relative_end": **********.749622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.749872, "relative_start": 0.9286289215087891, "end": **********.749872, "relative_end": **********.749872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.751091, "relative_start": 0.9298479557037354, "end": **********.751091, "relative_end": **********.751091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.751311, "relative_start": 0.9300680160522461, "end": **********.751311, "relative_end": **********.751311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.75243, "relative_start": 0.9311869144439697, "end": **********.75243, "relative_end": **********.75243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.752723, "relative_start": 0.9314799308776855, "end": **********.752723, "relative_end": **********.752723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.753874, "relative_start": 0.9326310157775879, "end": **********.753874, "relative_end": **********.753874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.754136, "relative_start": 0.9328930377960205, "end": **********.754136, "relative_end": **********.754136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.754843, "relative_start": 0.9335999488830566, "end": **********.754843, "relative_end": **********.754843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.755155, "relative_start": 0.9339120388031006, "end": **********.755155, "relative_end": **********.755155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.755414, "relative_start": 0.9341709613800049, "end": **********.755414, "relative_end": **********.755414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.755669, "relative_start": 0.9344260692596436, "end": **********.755669, "relative_end": **********.755669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.756884, "relative_start": 0.9356410503387451, "end": **********.756884, "relative_end": **********.756884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.757099, "relative_start": 0.9358558654785156, "end": **********.757099, "relative_end": **********.757099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.758231, "relative_start": 0.9369878768920898, "end": **********.758231, "relative_end": **********.758231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.758509, "relative_start": 0.9372658729553223, "end": **********.758509, "relative_end": **********.758509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.759735, "relative_start": 0.9384920597076416, "end": **********.759735, "relative_end": **********.759735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.76009, "relative_start": 0.9388470649719238, "end": **********.76009, "relative_end": **********.76009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.761026, "relative_start": 0.9397828578948975, "end": **********.761026, "relative_end": **********.761026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.761432, "relative_start": 0.9401888847351074, "end": **********.761432, "relative_end": **********.761432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.761664, "relative_start": 0.9404208660125732, "end": **********.761664, "relative_end": **********.761664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.761909, "relative_start": 0.9406659603118896, "end": **********.761909, "relative_end": **********.761909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.763092, "relative_start": 0.9418489933013916, "end": **********.763092, "relative_end": **********.763092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.763331, "relative_start": 0.9420878887176514, "end": **********.763331, "relative_end": **********.763331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.764496, "relative_start": 0.9432530403137207, "end": **********.764496, "relative_end": **********.764496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.764784, "relative_start": 0.9435410499572754, "end": **********.764784, "relative_end": **********.764784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.765985, "relative_start": 0.94474196434021, "end": **********.765985, "relative_end": **********.765985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.766264, "relative_start": 0.9450209140777588, "end": **********.766264, "relative_end": **********.766264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.766996, "relative_start": 0.9457528591156006, "end": **********.766996, "relative_end": **********.766996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.767325, "relative_start": 0.9460818767547607, "end": **********.767325, "relative_end": **********.767325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.76758, "relative_start": 0.9463369846343994, "end": **********.76758, "relative_end": **********.76758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.767858, "relative_start": 0.9466149806976318, "end": **********.767858, "relative_end": **********.767858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.769071, "relative_start": 0.9478280544281006, "end": **********.769071, "relative_end": **********.769071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.769299, "relative_start": 0.9480559825897217, "end": **********.769299, "relative_end": **********.769299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.770468, "relative_start": 0.9492249488830566, "end": **********.770468, "relative_end": **********.770468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.770779, "relative_start": 0.9495358467102051, "end": **********.770779, "relative_end": **********.770779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.771958, "relative_start": 0.9507150650024414, "end": **********.771958, "relative_end": **********.771958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.772231, "relative_start": 0.9509880542755127, "end": **********.772231, "relative_end": **********.772231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.77296, "relative_start": 0.9517168998718262, "end": **********.77296, "relative_end": **********.77296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.773295, "relative_start": 0.9520518779754639, "end": **********.773295, "relative_end": **********.773295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.773561, "relative_start": 0.9523179531097412, "end": **********.773561, "relative_end": **********.773561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.773838, "relative_start": 0.9525949954986572, "end": **********.773838, "relative_end": **********.773838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.775044, "relative_start": 0.9538009166717529, "end": **********.775044, "relative_end": **********.775044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.775272, "relative_start": 0.954028844833374, "end": **********.775272, "relative_end": **********.775272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.776711, "relative_start": 0.955467939376831, "end": **********.776711, "relative_end": **********.776711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.777079, "relative_start": 0.9558360576629639, "end": **********.777079, "relative_end": **********.777079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.778276, "relative_start": 0.9570329189300537, "end": **********.778276, "relative_end": **********.778276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.77853, "relative_start": 0.9572868347167969, "end": **********.77853, "relative_end": **********.77853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.779275, "relative_start": 0.9580318927764893, "end": **********.779275, "relative_end": **********.779275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.779604, "relative_start": 0.9583609104156494, "end": **********.779604, "relative_end": **********.779604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.779859, "relative_start": 0.9586160182952881, "end": **********.779859, "relative_end": **********.779859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.780127, "relative_start": 0.9588840007781982, "end": **********.780127, "relative_end": **********.780127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.781333, "relative_start": 0.960089921951294, "end": **********.781333, "relative_end": **********.781333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.781562, "relative_start": 0.9603190422058105, "end": **********.781562, "relative_end": **********.781562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.782715, "relative_start": 0.9614720344543457, "end": **********.782715, "relative_end": **********.782715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.783022, "relative_start": 0.9617788791656494, "end": **********.783022, "relative_end": **********.783022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.784206, "relative_start": 0.9629628658294678, "end": **********.784206, "relative_end": **********.784206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.784477, "relative_start": 0.9632339477539062, "end": **********.784477, "relative_end": **********.784477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.785214, "relative_start": 0.9639708995819092, "end": **********.785214, "relative_end": **********.785214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.785544, "relative_start": 0.9643008708953857, "end": **********.785544, "relative_end": **********.785544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.7858, "relative_start": 0.9645569324493408, "end": **********.7858, "relative_end": **********.7858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.786075, "relative_start": 0.964832067489624, "end": **********.786075, "relative_end": **********.786075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.787322, "relative_start": 0.9660789966583252, "end": **********.787322, "relative_end": **********.787322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.787557, "relative_start": 0.9663138389587402, "end": **********.787557, "relative_end": **********.787557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.788711, "relative_start": 0.9674680233001709, "end": **********.788711, "relative_end": **********.788711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.789018, "relative_start": 0.9677748680114746, "end": **********.789018, "relative_end": **********.789018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.790215, "relative_start": 0.9689719676971436, "end": **********.790215, "relative_end": **********.790215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.790478, "relative_start": 0.9692349433898926, "end": **********.790478, "relative_end": **********.790478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.791215, "relative_start": 0.9699718952178955, "end": **********.791215, "relative_end": **********.791215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.793099, "relative_start": 0.971855878829956, "end": **********.793099, "relative_end": **********.793099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.793812, "relative_start": 0.9725689888000488, "end": **********.793812, "relative_end": **********.793812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.794145, "relative_start": 0.9729020595550537, "end": **********.794145, "relative_end": **********.794145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.795391, "relative_start": 0.9741480350494385, "end": **********.795391, "relative_end": **********.795391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.795624, "relative_start": 0.9743809700012207, "end": **********.795624, "relative_end": **********.795624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.796788, "relative_start": 0.9755449295043945, "end": **********.796788, "relative_end": **********.796788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.797101, "relative_start": 0.9758579730987549, "end": **********.797101, "relative_end": **********.797101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.798284, "relative_start": 0.9770410060882568, "end": **********.798284, "relative_end": **********.798284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.798557, "relative_start": 0.9773139953613281, "end": **********.798557, "relative_end": **********.798557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.798981, "relative_start": 0.9777379035949707, "end": **********.798981, "relative_end": **********.798981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.80034, "relative_start": 0.9790968894958496, "end": **********.80034, "relative_end": **********.80034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.801608, "relative_start": 0.9803650379180908, "end": **********.801608, "relative_end": **********.801608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.801928, "relative_start": 0.9806849956512451, "end": **********.801928, "relative_end": **********.801928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.804417, "relative_start": 0.9831738471984863, "end": **********.804417, "relative_end": **********.804417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.805536, "relative_start": 0.9842929840087891, "end": **********.805536, "relative_end": **********.805536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.806084, "relative_start": 0.9848408699035645, "end": **********.806084, "relative_end": **********.806084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.heading", "start": **********.806618, "relative_start": 0.985374927520752, "end": **********.806618, "relative_end": **********.806618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": **********.807941, "relative_start": 0.9866979122161865, "end": **********.807941, "relative_end": **********.807941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.header.index", "start": **********.809598, "relative_start": 0.9883549213409424, "end": **********.809598, "relative_end": **********.809598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.actions", "start": **********.810163, "relative_start": 0.9889199733734131, "end": **********.810163, "relative_end": **********.810163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.813351, "relative_start": 0.9921078681945801, "end": **********.813351, "relative_end": **********.813351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.814059, "relative_start": 0.9928159713745117, "end": **********.814059, "relative_end": **********.814059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.815367, "relative_start": 0.9941239356994629, "end": **********.815367, "relative_end": **********.815367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.815953, "relative_start": 0.9947099685668945, "end": **********.815953, "relative_end": **********.815953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.817146, "relative_start": 0.9959030151367188, "end": **********.817146, "relative_end": **********.817146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.817588, "relative_start": 0.996345043182373, "end": **********.817588, "relative_end": **********.817588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.818819, "relative_start": 0.9975759983062744, "end": **********.818819, "relative_end": **********.818819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": **********.819398, "relative_start": 0.998154878616333, "end": **********.819398, "relative_end": **********.819398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.82195, "relative_start": 1.0007069110870361, "end": **********.824565, "relative_end": **********.824565, "duration": 0.0026149749755859375, "duration_str": "2.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 50242432, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 230, "nb_templates": 230, "templates": [{"name": "1x filament.admin.resources.menu-item-resource.pages.manage-menu-items", "param_count": null, "params": [], "start": **********.558488, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.phpfilament.admin.resources.menu-item-resource.pages.manage-menu-items", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ffilament%2Fadmin%2Fresources%2Fmenu-item-resource%2Fpages%2Fmanage-menu-items.blade.php&line=1", "ajax": false, "filename": "manage-menu-items.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.admin.resources.menu-item-resource.pages.manage-menu-items"}, {"name": "96x filament::components.icon", "param_count": null, "params": [], "start": **********.562706, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 96, "name_original": "filament::components.icon"}, {"name": "63x filament::components.button.index", "param_count": null, "params": [], "start": **********.563756, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 63, "name_original": "filament::components.button.index"}, {"name": "44x filament::components.loading-indicator", "param_count": null, "params": [], "start": **********.565792, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}, "render_count": 44, "name_original": "filament::components.loading-indicator"}, {"name": "17x filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "param_count": null, "params": [], "start": **********.693689, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/partials/wordpress-menu-item.blade.phpfilament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ffilament%2Fadmin%2Fresources%2Fmenu-item-resource%2Fpartials%2Fwordpress-menu-item.blade.php&line=1", "ajax": false, "filename": "wordpress-menu-item.blade.php", "line": "?"}, "render_count": 17, "name_original": "filament.admin.resources.menu-item-resource.partials.wordpress-menu-item"}, {"name": "1x filament::components.modal.index", "param_count": null, "params": [], "start": **********.801912, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.modal.index"}, {"name": "1x filament::components.icon-button", "param_count": null, "params": [], "start": **********.804394, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.icon-button"}, {"name": "1x filament::components.modal.heading", "param_count": null, "params": [], "start": **********.806601, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/heading.blade.phpfilament::components.modal.heading", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.modal.heading"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": **********.807916, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament-panels::components.header.index", "param_count": null, "params": [], "start": **********.809579, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/header/index.blade.phpfilament-panels::components.header.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.header.index"}, {"name": "1x filament::components.actions", "param_count": null, "params": [], "start": **********.810146, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/actions.blade.phpfilament::components.actions", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.actions"}, {"name": "2x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.81333, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": **********.819379, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}]}, "queries": {"count": 195, "nb_statements": 193, "nb_visible_statements": 195, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08661999999999992, "accumulated_duration_str": "86.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 93 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.313706, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 2.297}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.346245, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 2.297, "width_percent": 2.794}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.357964, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 5.091, "width_percent": 2.147}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 10, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.416815, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:726", "source": {"index": 9, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=726", "ajax": false, "filename": "ManageMenuItems.php", "line": "726"}, "connection": "auvista", "explain": null, "start_percent": 7.239, "width_percent": 0}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4175792, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 7.239, "width_percent": 1.524}, {"sql": "select * from `menus` where `menus`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 185}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 783}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 24, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.420238, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:185", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=185", "ajax": false, "filename": "ManageMenuItems.php", "line": "185"}, "connection": "auvista", "explain": null, "start_percent": 8.762, "width_percent": 0.612}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.422028, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 9.374, "width_percent": 0.6}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.42436, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 9.975, "width_percent": 1.004}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 24, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}], "start": **********.4269822, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 10.979, "width_percent": 0.508}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 24, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}], "start": **********.4284859, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 11.487, "width_percent": 0.427}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 24, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}], "start": **********.42988, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 11.914, "width_percent": 0.427}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 24, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}], "start": **********.431508, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 12.341, "width_percent": 0.647}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.433162, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 12.988, "width_percent": 0.6}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.434656, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 13.588, "width_percent": 0.589}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.436141, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 14.177, "width_percent": 0.485}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 816}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.4375138, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 14.662, "width_percent": 0.45}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.438873, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 15.112, "width_percent": 0.416}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.440213, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 15.528, "width_percent": 0.416}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4415379, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 15.943, "width_percent": 0.462}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.44292, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 16.405, "width_percent": 0.404}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.444238, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 16.809, "width_percent": 0.404}, {"sql": "update `menu_items` set `order` = 6, `menu_items`.`updated_at` = '2025-07-17 11:04:27' where `id` = 32", "type": "query", "params": [], "bindings": [6, "2025-07-17 11:04:27", 32], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 804}, {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4457579, "duration": 0.00956, "duration_str": "9.56ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:804", "source": {"index": 14, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 804}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=804", "ajax": false, "filename": "ManageMenuItems.php", "line": "804"}, "connection": "auvista", "explain": null, "start_percent": 17.213, "width_percent": 11.037}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4598691, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:782", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 782}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=782", "ajax": false, "filename": "ManageMenuItems.php", "line": "782"}, "connection": "auvista", "explain": null, "start_percent": 28.25, "width_percent": 0.935}, {"sql": "update `menu_items` set `order` = 7, `menu_items`.`updated_at` = '2025-07-17 11:04:27' where `id` = 65", "type": "query", "params": [], "bindings": [7, "2025-07-17 11:04:27", 65], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 804}, {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 729}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.461513, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:804", "source": {"index": 14, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 804}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=804", "ajax": false, "filename": "ManageMenuItems.php", "line": "804"}, "connection": "auvista", "explain": null, "start_percent": 29.185, "width_percent": 0.543}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 731}, {"index": 10, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.468802, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:731", "source": {"index": 9, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 731}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=731", "ajax": false, "filename": "ManageMenuItems.php", "line": "731"}, "connection": "auvista", "explain": null, "start_percent": 29.728, "width_percent": 0}, {"sql": "select * from `menus` where `menus`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 734}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 934}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.469086, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:734", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=734", "ajax": false, "filename": "ManageMenuItems.php", "line": "734"}, "connection": "auvista", "explain": null, "start_percent": 29.728, "width_percent": 0.82}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.5177531, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 30.547, "width_percent": 1.166}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.5199032, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 31.713, "width_percent": 0.716}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.521545, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 32.429, "width_percent": 0.577}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.522824, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 33.006, "width_percent": 0.45}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.5239968, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 33.456, "width_percent": 0.473}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.5252209, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 33.93, "width_percent": 1.051}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.526949, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 34.98, "width_percent": 0.496}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.5281851, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 35.477, "width_percent": 0.531}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.529491, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 36.008, "width_percent": 0.623}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.5308712, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 36.631, "width_percent": 0.52}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.5321472, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 37.151, "width_percent": 0.543}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.5333278, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 37.693, "width_percent": 0.462}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.534339, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 38.155, "width_percent": 0.45}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.535336, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 38.605, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.53627, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 38.998, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.537212, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 39.39, "width_percent": 0.473}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.5382261, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 39.864, "width_percent": 0.381}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.539157, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 40.245, "width_percent": 0.393}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.542316, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 40.637, "width_percent": 1.235}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.544128, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 41.873, "width_percent": 0.473}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.546046, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 42.346, "width_percent": 0.889}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.5477839, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 43.235, "width_percent": 0.45}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.5494442, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 43.685, "width_percent": 0.508}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.552907, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 44.193, "width_percent": 0.623}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5731351, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 44.816, "width_percent": 0.993}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.575044, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 45.809, "width_percent": 0.427}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.576047, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 46.236, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.57698, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 46.629, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.578024, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 47.021, "width_percent": 0.416}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.578995, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 47.437, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.579924, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 47.83, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.580856, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 48.222, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.581788, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 48.615, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.582721, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 49.007, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.58365, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 49.4, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.58467, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 49.792, "width_percent": 0.427}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.585725, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 50.219, "width_percent": 0.404}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.586668, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 50.623, "width_percent": 0.404}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.58763, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 51.027, "width_percent": 0.866}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.5891678, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 51.893, "width_percent": 0.485}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.590341, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 52.378, "width_percent": 0.543}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.591486, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 52.921, "width_percent": 0.416}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.592495, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 53.336, "width_percent": 0.439}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.5934172, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 53.775, "width_percent": 0.358}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5947149, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 54.133, "width_percent": 0.427}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5959451, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 54.56, "width_percent": 0.358}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.597003, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 54.918, "width_percent": 0.358}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.598237, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 55.276, "width_percent": 0.335}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.602921, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 55.611, "width_percent": 1.224}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.604941, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 56.834, "width_percent": 0.496}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.606115, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 57.331, "width_percent": 0.404}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.607072, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 57.735, "width_percent": 0.381}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.608002, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 58.116, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.608938, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 58.508, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.609879, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 58.901, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.610809, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 59.293, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.611743, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 59.686, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.61278, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 60.079, "width_percent": 0.416}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.613743, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 60.494, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.6146789, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 60.887, "width_percent": 0.462}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.615667, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 61.348, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.6165981, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 61.741, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.617526, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 62.133, "width_percent": 0.404}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.618471, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 62.538, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.619594, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 62.93, "width_percent": 0.589}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.6207302, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 63.519, "width_percent": 0.52}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.6217642, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 64.038, "width_percent": 0.52}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.622767, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 64.558, "width_percent": 0.485}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6241748, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 65.043, "width_percent": 0.45}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.625422, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 65.493, "width_percent": 0.358}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.62648, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 65.851, "width_percent": 0.358}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6276321, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 66.209, "width_percent": 0.335}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.634042, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 66.544, "width_percent": 1.004}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.636235, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 67.548, "width_percent": 0.52}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.637334, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 68.067, "width_percent": 0.404}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.6384559, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 68.471, "width_percent": 0.427}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.639457, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 68.899, "width_percent": 0.381}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.639991, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 69.28, "width_percent": 0.358}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6404932, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 69.637, "width_percent": 0.346}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6409872, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 69.984, "width_percent": 0.346}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.64148, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 70.33, "width_percent": 0.346}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.641975, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 70.677, "width_percent": 0.439}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6425471, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 71.115, "width_percent": 0.346}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6430452, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 71.462, "width_percent": 0.346}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6435401, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 71.808, "width_percent": 0.335}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.644023, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.143, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6444738, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.443, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6449108, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.731, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6453578, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.032, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.645805, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.332, "width_percent": 0.3}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.646253, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.632, "width_percent": 0.312}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.646727, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.944, "width_percent": 0.312}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6475751, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.255, "width_percent": 0.358}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6481962, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.613, "width_percent": 0.312}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6486828, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.925, "width_percent": 0.3}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.649266, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.225, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.652922, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.514, "width_percent": 0.854}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6539571, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.368, "width_percent": 0.335}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.65445, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.703, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.654902, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.003, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6553488, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.303, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6557958, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.603, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.656249, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.903, "width_percent": 0.381}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.656765, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 78.284, "width_percent": 0.323}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6572309, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 78.608, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.657682, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 78.908, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.658135, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 79.208, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.658577, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 79.497, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.659019, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 79.785, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.659464, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.085, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6599169, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.386, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6603699, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.686, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6608078, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.974, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6612618, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.275, "width_percent": 0.289}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.661692, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.563, "width_percent": 0.416}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662263, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.979, "width_percent": 0.289}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.663094, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 82.267, "width_percent": 0.358}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.663718, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 82.625, "width_percent": 0.3}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.664197, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 82.925, "width_percent": 0.3}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.664783, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 83.226, "width_percent": 0.277}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.668345, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 83.503, "width_percent": 0.762}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6693032, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.265, "width_percent": 0.312}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.669774, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.576, "width_percent": 0.312}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6702318, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.888, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6706798, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.188, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.671129, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.488, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.671582, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.789, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6720238, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.077, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.672472, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.377, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.672925, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.677, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.673364, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.966, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6738088, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 87.266, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674255, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 87.566, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674703, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 87.867, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6751509, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.167, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.675601, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.467, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6760392, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.755, "width_percent": 0.312}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.676503, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.067, "width_percent": 0.289}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6769412, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.356, "width_percent": 0.312}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6774118, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.668, "width_percent": 0.3}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6782541, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.968, "width_percent": 0.346}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6788661, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.314, "width_percent": 0.312}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.67936, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.626, "width_percent": 0.3}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.67994, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.926, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.681176, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.215, "width_percent": 0.566}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6821082, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.78, "width_percent": 0.508}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6827788, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.288, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.683226, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.588, "width_percent": 0.393}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.683753, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.981, "width_percent": 0.346}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.684238, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.327, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.684691, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.627, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6851249, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.916, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.685572, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.216, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6860049, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.505, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.686451, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.805, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.686884, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.094, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6873288, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.394, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6877759, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.694, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6882248, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.994, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6886709, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.294, "width_percent": 0.3}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6891248, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.594, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.689569, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.883, "width_percent": 0.289}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.69, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.172, "width_percent": 0.312}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6904788, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.483, "width_percent": 0.3}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.691323, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.783, "width_percent": 0.358}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6919432, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.141, "width_percent": 0.312}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.692427, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.453, "width_percent": 0.3}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.693009, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.753, "width_percent": 0.289}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7918792, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 99.042, "width_percent": 0.958}]}, "models": {"data": {"App\\Models\\MenuItem": {"value": 255, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Category": {"value": 210, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\StaticPage": {"value": 196, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FStaticPage.php&line=1", "ajax": false, "filename": "StaticPage.php", "line": "?"}}, "App\\Models\\Brand": {"value": 126, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Post": {"value": 113, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 49, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Menu": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 992, "is_counter": true}, "livewire": {"data": {"app.filament.resources.menu-item-resource.pages.manage-menu-items #J7YibGkEpP1Zwt1Trr0M": "array:4 [\n  \"data\" => array:51 [\n    \"isMenuReordering\" => false\n    \"newMenuItemData\" => null\n    \"selectedTab\" => \"custom\"\n    \"customUrl\" => null\n    \"customTitle\" => null\n    \"selectedPages\" => []\n    \"selectedPosts\" => []\n    \"selectedCategories\" => []\n    \"selectedBrands\" => []\n    \"selectedMenuItems\" => []\n    \"editingMenuItem\" => []\n    \"refreshCounter\" => 2\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:2 [\n      \"type\" => array:1 [\n        \"value\" => null\n      ]\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:3 [\n      \"url\" => true\n      \"type\" => true\n      \"status\" => true\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.menu-item-resource.pages.manage-menu-items\"\n  \"component\" => \"App\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems\"\n  \"id\" => \"J7YibGkEpP1Zwt1Trr0M\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "1.01s", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-662103258 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-662103258\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2105540372 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E5i5PJh9l9VwKtI1wpqPfTuNVse14pMEKeu2ITQy</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"2137 characters\">{&quot;data&quot;:{&quot;isMenuReordering&quot;:false,&quot;newMenuItemData&quot;:null,&quot;selectedTab&quot;:&quot;custom&quot;,&quot;customUrl&quot;:null,&quot;customTitle&quot;:null,&quot;selectedPages&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedPosts&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedCategories&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedBrands&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedMenuItems&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editingMenuItem&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;refreshCounter&quot;:0,&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;type&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;status&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areFormStateUpdateHooksDisabledForTesting&quot;:false,&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[{&quot;url&quot;:true,&quot;type&quot;:true,&quot;status&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;J7YibGkEpP1Zwt1Trr0M&quot;,&quot;name&quot;:&quot;app.filament.resources.menu-item-resource.pages.manage-menu-items&quot;,&quot;path&quot;:&quot;admin\\/menu-items\\/5&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;vi&quot;},&quot;checksum&quot;:&quot;cb0e710c8034efb7680768c4f9ecfff40795be012dc6c8a3840bc7e0823238e1&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__dispatch</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">reorderMenuItem</span>\"\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>17</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>18</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>children</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>\n                      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n                      \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>18</span>\n                      \"<span class=sf-dump-key>children</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n                          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n                          \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>19</span>\n                        </samp>]\n                        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>21</span>\n                          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n                          \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>19</span>\n                        </samp>]\n                        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>22</span>\n                          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n                          \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>19</span>\n                        </samp>]\n                        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>23</span>\n                          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n                          \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>19</span>\n                        </samp>]\n                      </samp>]\n                    </samp>]\n                    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>24</span>\n                      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n                      \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>18</span>\n                    </samp>]\n                    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>25</span>\n                      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n                      \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>18</span>\n                    </samp>]\n                    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>26</span>\n                      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n                      \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>18</span>\n                    </samp>]\n                    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>27</span>\n                      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n                      \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>18</span>\n                    </samp>]\n                  </samp>]\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>28</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>29</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>30</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>31</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>5</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>32</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>6</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>7</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n              </samp>]\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105540372\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1757266791 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3222</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">https://auvista.test/admin/menu-items/5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkVzL0pVMHd6clN4YTRFOVNRRzVaRnc9PSIsInZhbHVlIjoiWHNQQjVQeFdWeGp0NVJXZEE5cnMvTzhxN2RpeUlZeEJWUFVsSVZhc2FhMkttWTM5YU5EYm9EMGo5Y29OWWZURVVHNS9McXJkdmFId21EVWthSzlXTGcycDF4YVdNZnV5QVpkSmtwSmVCQjhiRVpBa05qNEVpSmFIbnhoRGRqVFMiLCJtYWMiOiJiNGM2NjA0ZmZlNDU2Njc0NGMyZTA3NzIzNDg3OTI4OGQxOGVjODM4MzRlMTNjMDA2OGViZWFiOWM3MDdjNzE5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImNnbXh4V2lwazJGYVJESDRwWEZoY1E9PSIsInZhbHVlIjoiRU4vSkFDcHBCNGh5QVJJS1pBZ3NQZ1lTSFU3SXZ6b2dUZFFJNFQ3bFlOd25ZUmkwaTRqaHlRcm9jRFBrdzJUeldjbGhpTmlJY1IzL2J6b0ZRNUlrNSs5dmpNajFTcGhBcDh3YUlzL2RWTHpIUmNLbE9ybG9kK1JVU1VqTFl6QjYiLCJtYWMiOiI2OGIxMWQ4M2Q1OTFkMzgyZGEyYTYxNGI2ZTJkZjgyNzNkNjc1ZWZjOTc5OWNhZDg5NjhjMmQ5MzA0ZGM2OGQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1757266791\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-806706628 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E5i5PJh9l9VwKtI1wpqPfTuNVse14pMEKeu2ITQy</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T5AEVWUx3GFIfIinUvq16vAwcsjmwqju4fDR0Pfs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806706628\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-786379096 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 04:04:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-786379096\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1704037776 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E5i5PJh9l9VwKtI1wpqPfTuNVse14pMEKeu2ITQy</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">https://auvista.test/admin/menu-items/5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$2uJGkMnwk7Lj203GgLThU.EI1Z7VNTd1oXu5zgFDqiWVi2sEnDYEy</span>\"\n  \"<span class=sf-dump-key>current_menu_id</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>filament</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>notifications</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f68cf80-f4f1-43bd-b386-7a100e914dab</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Th&#7913; t&#7921; v&#224; c&#7845;u tr&#250;c menu &#273;a c&#7845;p &#273;&#227; &#273;&#432;&#7907;c l&#432;u</span>\"\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Menu &#273;&#227; &#273;&#432;&#7907;c c&#7853;p nh&#7853;t</span>\"\n        \"<span class=sf-dump-key>view</span>\" => \"<span class=sf-dump-str title=\"36 characters\">filament-notifications::notification</span>\"\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f68cf80-fa78-433f-b5c0-1e77490ed87a</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Th&#7913; t&#7921; menu &#273;&#227; &#273;&#432;&#7907;c c&#7853;p nh&#7853;t.</span>\"\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">C&#7853;p nh&#7853;t th&#224;nh c&#244;ng</span>\"\n        \"<span class=sf-dump-key>view</span>\" => \"<span class=sf-dump-str title=\"36 characters\">filament-notifications::notification</span>\"\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704037776\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}