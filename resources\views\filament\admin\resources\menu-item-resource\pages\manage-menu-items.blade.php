<x-filament-panels::page>
    <div class="wordpress-menu-manager">
        <!-- Header -->
        <div class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ $menu->name }}</h1>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Quản lý cấu trúc menu và các liên kết
                    </p>
                </div>

                <div class="flex items-center justify-center gap-3">
                    <x-filament::button wire:click="forceReloadMenuItems" color="gray" outlined size="sm">
                        <x-filament::icon icon="heroicon-o-arrow-path" class="h-4 w-4 mr-1" style="float: left;" />
                        Làm mới
                    </x-filament::button>

                    <x-filament::button wire:click="saveMenu" color="primary" size="sm">
                        <x-filament::icon icon="heroicon-o-check" class="h-4 w-4 mr-1" style="float: left;" />
                        L<PERSON><PERSON>
                    </x-filament::button>

                    <x-filament::button
                        href="{{ \App\Filament\Resources\MenuResource::getUrl('index') }}"
                        color="gray"
                        outlined
                        size="sm"
                    >
                        <x-filament::icon icon="heroicon-o-arrow-left" class="h-4 w-4 mr-1" style="float: left;" />
                        Quay lại
                    </x-filament::button>
                </div>
            </div>
        </div>

        <!-- Quick Help -->
        <div class="mb-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex items-start">
                <x-filament::icon icon="heroicon-o-information-circle" class="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
                <div class="text-sm text-blue-800 dark:text-blue-200">
                    <p class="font-medium mb-1">Hướng dẫn nhanh:</p>
                    <ul class="space-y-1 text-blue-700 dark:text-blue-300">
                        <li>• Kéo thả để sắp xếp thứ tự menu</li>
                        <li>• Kéo sang phải để tạo menu con</li>
                        <li>• Click vào mũi tên để mở rộng/thu gọn</li>
                        <li>• Click "Chỉnh sửa" để thay đổi chi tiết</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Main Content Grid - WordPress Style 2 Columns -->
        <div class="flex flex-col lg:flex-row gap-6 lg:gap-8 min-h-[600px]">
            <!-- Left Column: Add Menu Items (30% width) -->
            <div class="lg:w-96 flex-shrink-0">
                <!-- Column Label for Mobile -->
                <div class="lg:hidden mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <h2 class="text-lg font-semibold text-blue-900 dark:text-blue-100 flex items-center">
                        <x-filament::icon icon="heroicon-o-plus-circle" class="h-5 w-5 mr-2" />
                        Bước 1: Thêm mục menu
                    </h2>
                    <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">Chọn các mục để thêm vào menu</p>
                </div>
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm h-fit">
                    <div class="px-4 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                            <x-filament::icon icon="heroicon-o-plus-circle" class="h-5 w-5 mr-2 text-primary-600" />
                            Thêm mục menu
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">Chọn các mục để thêm vào menu của bạn</p>
                    </div>

                    <div x-data="{ activeSection: 'pages' }" class="divide-y divide-gray-200 dark:divide-gray-700">
                        <!-- Pages Section -->
                        <div class="wordpress-accordion-item mx-2">
                            <button
                                @click="activeSection = activeSection === 'pages' ? null : 'pages'"
                                class="flex justify-between items-center w-full px-5 py-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                                x-bind:class="{ 'bg-gray-50 dark:bg-gray-700/50': activeSection === 'pages' }"
                            >
                                <div class="flex items-center space-x-3">
                                    <x-filament::icon icon="heroicon-o-document-text" class="h-5 w-5 text-gray-500" />
                                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Trang</span>
                                </div>
                                <x-filament::icon
                                    icon="heroicon-o-chevron-down"
                                    class="h-4 w-4 text-gray-400 transition-transform"
                                    x-bind:class="{ 'rotate-180': activeSection === 'pages' }"
                                />
                            </button>

                            <div x-show="activeSection === 'pages'"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                                 x-transition:enter-end="opacity-100 transform translate-y-0"
                                 class="p-4 bg-white dark:bg-gray-800">

                                <div class="space-y-3">
                                    <!-- Search box -->
                                    <div class="relative">
                                        <x-filament::icon icon="heroicon-o-magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 mr-2 text-gray-400" />
                                        <input
                                            type="text"
                                            placeholder="Tìm kiếm trang..."
                                            class="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
                                        />
                                    </div>

                                    <!-- Pages list -->
                                    <div class="max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md">
                                        @foreach($this->getViewData()['pages'] as $page)
                                            <label class="flex items-center space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 border-b border-gray-100 dark:border-gray-700 last:border-b-0 cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    value="{{ $page->id }}"
                                                    wire:model="selectedPages"
                                                    class="mx-2 rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-gray-600 dark:bg-gray-700"
                                                />
                                                <div class="flex-1 min-w-0">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{{ $page->title }}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ $page->slug }}</div>
                                                </div>
                                            </label>
                                        @endforeach
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex justify-between items-center pt-3 border-t border-gray-200 dark:border-gray-600">
                                        <label class="flex items-center space-x-3 text-sm text-gray-600 dark:text-gray-400">
                                            <input
                                                type="checkbox"
                                                class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-gray-600 dark:bg-gray-700"
                                                @click="
                                                    const checkboxes = document.querySelectorAll('input[wire\\:model=\'selectedPages\']');
                                                    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

                                                    if (!allChecked) {
                                                        const values = Array.from(checkboxes).map(cb => cb.value);
                                                        $wire.set('selectedPages', values);
                                                    } else {
                                                        $wire.set('selectedPages', []);
                                                    }
                                                "
                                            />
                                            <span>Chọn tất cả</span>
                                        </label>

                                        <x-filament::button
                                            wire:click="createPageMenuItems"
                                            x-on:click="activeSection = null"
                                            size="sm"
                                            color="primary"
                                            class="px-4 py-2"
                                        >
                                            <x-filament::icon icon="heroicon-o-plus" class="h-4 w-4 mr-2" />
                                            Thêm vào Menu
                                        </x-filament::button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Posts Section -->
                        <div class="wordpress-accordion-item mx-2">
                            <button
                                @click="activeSection = activeSection === 'posts' ? null : 'posts'"
                                class="flex justify-between items-center w-full px-5 py-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                                x-bind:class="{ 'bg-gray-50 dark:bg-gray-700/50': activeSection === 'posts' }"
                            >
                                <div class="flex items-center space-x-3">
                                    <x-filament::icon icon="heroicon-o-newspaper" class="h-5 w-5 text-gray-500" />
                                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Bài viết</span>
                                </div>
                                <x-filament::icon
                                    icon="heroicon-o-chevron-down"
                                    class="h-4 w-4 text-gray-400 transition-transform"
                                    x-bind:class="{ 'rotate-180': activeSection === 'posts' }"
                                />
                            </button>

                            <div x-show="activeSection === 'posts'"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                                 x-transition:enter-end="opacity-100 transform translate-y-0"
                                 class="p-4 bg-white dark:bg-gray-800">

                                <div class="space-y-3">
                                    <!-- Search box -->
                                    <div class="relative">
                                        <x-filament::icon icon="heroicon-o-magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                        <input
                                            type="text"
                                            placeholder="Tìm kiếm bài viết..."
                                            class="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
                                        />
                                    </div>

                                    <!-- Posts list -->
                                    <div class="max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md">
                                        @foreach($this->getViewData()['posts'] as $post)
                                            <label class="flex items-center space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 border-b border-gray-100 dark:border-gray-700 last:border-b-0 cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    value="{{ $post->id }}"
                                                    wire:model="selectedPosts"
                                                    class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-gray-600 dark:bg-gray-700"
                                                />
                                                <div class="flex-1 min-w-0">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{{ $post->title }}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ $post->slug }}</div>
                                                </div>
                                            </label>
                                        @endforeach
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex justify-between items-center pt-3 border-t border-gray-200 dark:border-gray-600">
                                        <label class="flex items-center space-x-3 text-sm text-gray-600 dark:text-gray-400">
                                            <input
                                                type="checkbox"
                                                class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-gray-600 dark:bg-gray-700"
                                                @click="
                                                    const checkboxes = document.querySelectorAll('input[wire\\:model=\'selectedPosts\']');
                                                    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

                                                    if (!allChecked) {
                                                        const values = Array.from(checkboxes).map(cb => cb.value);
                                                        $wire.set('selectedPosts', values);
                                                    } else {
                                                        $wire.set('selectedPosts', []);
                                                    }
                                                "
                                            />
                                            <span>Chọn tất cả</span>
                                        </label>

                                        <x-filament::button
                                            wire:click="createPostMenuItems"
                                            x-on:click="activeSection = null"
                                            size="sm"
                                            color="primary"
                                            class="px-4 py-2"
                                        >
                                            <x-filament::icon icon="heroicon-o-plus" class="h-4 w-4 mr-2" />
                                            Thêm vào Menu
                                        </x-filament::button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Custom Links Section -->
                        <div class="wordpress-accordion-item mx-2">
                            <button
                                @click="activeSection = activeSection === 'custom' ? null : 'custom'"
                                class="flex justify-between items-center w-full px-5 py-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                                x-bind:class="{ 'bg-gray-50 dark:bg-gray-700/50': activeSection === 'custom' }"
                            >
                                <div class="flex items-center space-x-3">
                                    <x-filament::icon icon="heroicon-o-link" class="h-5 w-5 text-gray-500" />
                                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Liên kết tùy chỉnh</span>
                                </div>
                                <x-filament::icon
                                    icon="heroicon-o-chevron-down"
                                    class="h-4 w-4 text-gray-400 transition-transform"
                                    x-bind:class="{ 'rotate-180': activeSection === 'custom' }"
                                />
                            </button>

                            <div x-show="activeSection === 'custom'"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                                 x-transition:enter-end="opacity-100 transform translate-y-0"
                                 class="p-4 bg-white dark:bg-gray-800">

                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">URL</label>
                                        <input
                                            type="url"
                                            wire:model="customUrl"
                                            placeholder="https://example.com"
                                            class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
                                        />
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tiêu đề liên kết</label>
                                        <input
                                            type="text"
                                            wire:model="customTitle"
                                            placeholder="Nhập tiêu đề hiển thị"
                                            class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
                                        />
                                    </div>

                                    <x-filament::button
                                        wire:click="createCustomUrlMenuItem"
                                        x-on:click="activeSection = null"
                                        size="sm"
                                        color="primary"
                                        class="w-full px-4 py-3 mt-2"
                                    >
                                        <x-filament::icon icon="heroicon-o-plus" class="h-4 w-4 mr-2" />
                                        Thêm vào Menu
                                    </x-filament::button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Categories Section -->
                        <div class="wordpress-accordion-item mx-2">
                            <button
                                @click="activeSection = activeSection === 'categories' ? null : 'categories'"
                                class="flex justify-between items-center w-full px-5 py-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                                x-bind:class="{ 'bg-gray-50 dark:bg-gray-700/50': activeSection === 'categories' }"
                            >
                                <div class="flex items-center space-x-3">
                                    <x-filament::icon icon="heroicon-o-tag" class="h-5 w-5 text-gray-500" />
                                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Danh mục</span>
                                </div>
                                <x-filament::icon
                                    icon="heroicon-o-chevron-down"
                                    class="h-4 w-4 text-gray-400 transition-transform"
                                    x-bind:class="{ 'rotate-180': activeSection === 'categories' }"
                                />
                            </button>
                            
                            <div x-show="activeSection === 'categories'" class="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700" x-cloak>
                                <div class="max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-700 rounded-md mb-3">
                                    @foreach($this->getViewData()['categories'] as $category)
                                        <label class="flex items-center space-x-3 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                                            <input
                                                type="checkbox"
                                                value="{{ $category->id }}"
                                                wire:model="selectedCategories"
                                                class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:focus:border-primary-400"
                                            />
                                            <span class="text-sm">{{ $category->name }}</span>
                                        </label>
                                    @endforeach
                                </div>

                                <div class="flex justify-between items-center">
                                    <label class="flex items-center space-x-2">
                                        <input 
                                            type="checkbox" 
                                            class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:focus:border-primary-400"
                                            @click="
                                                const checkboxes = document.querySelectorAll('input[wire\\:model=\'selectedCategories\']');
                                                const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                                                
                                                if (!allChecked) {
                                                    // Nếu chưa chọn hết, chọn tất cả
                                                    const values = Array.from(checkboxes).map(cb => cb.value);
                                                    $wire.set('selectedCategories', values);
                                                } else {
                                                    // Nếu đã chọn hết, bỏ chọn tất cả
                                                    $wire.set('selectedCategories', []);
                                                }
                                            "
                                        />
                                        <span class="text-xs">Chọn tất cả</span>
                                    </label>
                                    
                                    <x-filament::button wire:click="createCategoryMenuItems" x-on:click="activeSection = null" size="sm">
                                        Thêm vào Menu
                                    </x-filament::button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Brands Section -->
                        <div class="wordpress-accordion-item mx-2">
                            <button
                                @click="activeSection = activeSection === 'brands' ? null : 'brands'"
                                class="flex justify-between items-center w-full px-5 py-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                                x-bind:class="{ 'bg-gray-50 dark:bg-gray-700/50': activeSection === 'brands' }"
                            >
                                <div class="flex items-center space-x-3">
                                    <x-filament::icon icon="heroicon-o-bolt" class="h-5 w-5 text-gray-500" />
                                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Thương hiệu</span>
                                </div>
                                <x-filament::icon
                                    icon="heroicon-o-chevron-down"
                                    class="h-4 w-4 text-gray-400 transition-transform"
                                    x-bind:class="{ 'rotate-180': activeSection === 'brands' }"
                                />
                            </button>
                            
                            <div x-show="activeSection === 'brands'" class="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700" x-cloak>
                                <div class="max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-700 rounded-md mb-3">
                                    @foreach($this->getViewData()['brands'] ?? [] as $brand)
                                        <label class="flex items-center space-x-3 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                                            <input
                                                type="checkbox"
                                                value="{{ $brand->id }}"
                                                wire:model="selectedBrands"
                                                class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:focus:border-primary-400"
                                            />
                                            <span class="text-sm">{{ $brand->name }}</span>
                                        </label>
                                    @endforeach
                                </div>

                                <div class="flex justify-between items-center">
                                    <label class="flex items-center space-x-2">
                                        <input 
                                            type="checkbox" 
                                            class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:focus:border-primary-400"
                                            @click="
                                                const checkboxes = document.querySelectorAll('input[wire\\:model=\'selectedBrands\']');
                                                const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                                                
                                                if (!allChecked) {
                                                    // Nếu chưa chọn hết, chọn tất cả
                                                    const values = Array.from(checkboxes).map(cb => cb.value);
                                                    $wire.set('selectedBrands', values);
                                                } else {
                                                    // Nếu đã chọn hết, bỏ chọn tất cả
                                                    $wire.set('selectedBrands', []);
                                                }
                                            "
                                        />
                                        <span class="text-xs">Chọn tất cả</span>
                                    </label>
                                    
                                    <x-filament::button wire:click="createBrandMenuItems" x-on:click="activeSection = null" size="sm">
                                        Thêm vào Menu
                                    </x-filament::button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Separator -->
            <div class="hidden lg:block w-px bg-gray-200 dark:bg-gray-700 self-stretch"></div>

            <!-- Right Column: Menu Structure (70% width) -->
            <div class="flex-1 min-w-0">
                <!-- Column Label for Mobile -->
                <div class="lg:hidden mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <h2 class="text-lg font-semibold text-green-900 dark:text-green-100 flex items-center">
                        <x-filament::icon icon="heroicon-o-list-bullet" class="h-5 w-5 mr-2" />
                        Bước 2: Sắp xếp menu
                    </h2>
                    <p class="text-sm text-green-700 dark:text-green-300 mt-1">Kéo thả để sắp xếp thứ tự và tạo menu con</p>
                </div>
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
                    <div class="px-4 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50 flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                                <x-filament::icon icon="heroicon-o-list-bullet" class="h-5 w-5 mr-2 text-primary-600" />
                                Cấu trúc Menu
                            </h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">Kéo thả để sắp xếp thứ tự và tạo menu con</p>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            @if(count($this->selectedMenuItems) > 0)
                                <x-filament::button 
                                    wire:click="$dispatch('open-modal', { id: 'bulk-delete-menu-items' })" 
                                    color="danger" 
                                    size="sm"
                                >
                                    <x-filament::icon icon="heroicon-m-trash" class="h-4 w-4 mr-1" />
                                    Xóa {{ count($this->selectedMenuItems) }} mục
                                </x-filament::button>
                            @endif
                        </div>
                    </div>
                    
                    <div class="p-4">
                        <div class="mb-3 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                            <label class="flex items-center">
                                <input type="checkbox" id="select-all-menu-items" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:focus:border-primary-400" @click="
                                    const checkboxes = document.querySelectorAll('.menu-item-checkbox');
                                    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                                    
                                    checkboxes.forEach(cb => {
                                        cb.checked = !allChecked;
                                    });
                                    
                                    // Cập nhật trạng thái checkbox vào Livewire
                                    const selectedIds = Array.from(checkboxes)
                                        .filter(cb => cb.checked)
                                        .map(cb => cb.value);
                                    
                                    $wire.set('selectedMenuItems', selectedIds);
                                ">
                                <span class="ml-2">Chọn tất cả</span>
                            </label>
                        </div>
                        
                        <div id="menu-structure" class="space-y-3 min-h-[300px] p-4">
                            @if(count($this->getViewData()['menuItems']) > 0)
                                @foreach($this->getViewData()['menuItems'] as $menuItem)
                                    @include('filament.admin.resources.menu-item-resource.partials.wordpress-menu-item', ['menuItem' => $menuItem])
                                @endforeach

                                <!-- Drop Zone -->
                                <div class="drop-zone border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center text-gray-500 dark:text-gray-400 hover:border-primary-400 hover:text-primary-600 transition-colors">
                                    <x-filament::icon icon="heroicon-o-plus" class="h-6 w-6 mx-auto mb-2" />
                                    <p class="text-sm">Kéo thả mục menu vào đây để thêm vào cuối danh sách</p>
                                </div>
                            @else
                                <div class="py-12 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                                    <div class="bg-gray-100 dark:bg-gray-700 rounded-full p-4 mb-4">
                                        <x-filament::icon icon="heroicon-o-list-bullet" class="h-8 w-8" />
                                    </div>
                                    <h3 class="text-lg font-medium mb-2">Chưa có mục menu nào</h3>
                                    <p class="text-sm text-center max-w-sm">
                                        Bắt đầu xây dựng menu bằng cách thêm các trang, bài viết, danh mục hoặc liên kết tùy chỉnh từ bên trái.
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Đảm bảo toàn bộ trang đã tải xong
            document.addEventListener('DOMContentLoaded', function () {
                // Đảm bảo Sortable library đã được tải
                if (typeof Sortable === 'undefined') {
                    console.error('Sortable library not found! Trying to load it...');
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js';
                    script.onload = initSortables;
                    document.head.appendChild(script);
                } else {
                    console.log('Sortable library found, initializing...');
                    initSortables(); // Bỏ setTimeout để khởi tạo nhanh hơn
                }
                
                // Khởi tạo khi có sự kiện từ Livewire
                document.addEventListener('livewire:initialized', function() {
                    console.log('Livewire initialized, setting up event listeners...');
                    
                    // Đảm bảo khởi tạo Sortable sau khi DOM đã hoàn thành
                    initSortables();
                    
                    // Khi Livewire cập nhật DOM
                    Livewire.hook('morph.updated', ({ el }) => {
                        if (el.id === 'menu-structure' || el.closest('#menu-structure')) {
                            console.log('Menu structure updated, reinitializing sortables...');
                            // Khởi tạo lại ngay lập tức, không trì hoãn
                            initSortables();
                        }
                    });
                    
                    // Khi có sự kiện menu-items-updated
                    window.addEventListener('menu-items-updated', function() {
                        console.log('Menu items updated event received');
                        initSortables();
                    });
                    
                    // Lắng nghe sự kiện refresh từ Livewire
                    Livewire.on('refresh', () => {
                        console.log('Refresh event received, reloading page...');
                        
                        // Hiển thị thông báo đang tải lại
                        const loadingMessage = document.createElement('div');
                        loadingMessage.className = 'fixed top-0 left-0 right-0 bg-blue-500 text-white py-2 text-center z-50';
                        loadingMessage.innerHTML = 'Đang tải lại cấu trúc menu...';
                        document.body.appendChild(loadingMessage);
                        
                        // Làm mới trang ngay lập tức để giảm hiệu ứng nhấp nháy
                        window.location.reload();
                    });
                    
                    // Lắng nghe sự kiện cập nhật cấu trúc menu
                    Livewire.on('update-menu-structure', () => {
                        console.log('Update menu structure event received, updating UI without reload...');
                        
                        // Hiển thị thông báo đang cập nhật
                        const processingMessage = document.createElement('div');
                        processingMessage.id = 'menu-processing-indicator';
                        processingMessage.className = 'fixed top-0 left-0 right-0 bg-blue-500 text-white py-2 text-center z-50';
                        processingMessage.innerHTML = 'Đang cập nhật giao diện menu...';
                        document.body.appendChild(processingMessage);
                        
                        // Tắt tất cả các sortable để tránh xung đột
                        destroyAllSortables();
                        
                        // Đánh dấu tất cả các menu item đang hiển thị để xóa
                        document.querySelectorAll('.menu-item').forEach(item => {
                            item.classList.add('menu-item-to-remove');
                        });
                        
                        // Tạo hiệu ứng đang tải
                        setTimeout(() => {
                            // Khởi tạo lại sortables - điều này sẽ tải lại DOM từ dữ liệu mới
                            initSortables();
                            
                            // Xóa thông báo
                            processingMessage.remove();
                            
                            // Hiển thị thông báo thành công
                            const successMessage = document.createElement('div');
                            successMessage.className = 'fixed top-0 left-0 right-0 bg-green-500 text-white py-2 text-center z-50';
                            successMessage.innerHTML = 'Cấu trúc menu đã được cập nhật thành công';
                            document.body.appendChild(successMessage);
                            
                            // Ẩn thông báo sau 3 giây
                            setTimeout(() => {
                                successMessage.remove();
                            }, 3000);
                        }, 500);
                    });
                });
            });
            
            // Mảng lưu trữ tất cả các instance Sortable để có thể hủy chúng khi cần
            let sortableInstances = [];
            let parentIndicator = null;
            let processingDrop = false; // Biến để kiểm soát không xử lý nhiều lần
            let itemIdMap = new Map(); // Cache để lưu trữ ID đã xử lý
            
            // Hàm hủy tất cả các instance Sortable hiện có
            function destroyAllSortables() {
                sortableInstances.forEach(instance => {
                    if (instance && typeof instance.destroy === 'function') {
                        instance.destroy();
                    }
                });
                sortableInstances = [];
                
                // Xóa bỏ indicator nếu tồn tại
                if (parentIndicator && parentIndicator.parentNode) {
                    parentIndicator.parentNode.removeChild(parentIndicator);
                    parentIndicator = null;
                }
            }
            
            // Hàm khởi tạo tất cả các Sortable
            function initSortables() {
                console.log('Initializing all sortables...');
                
                // Đầu tiên, hủy tất cả các sortable hiện có để tránh xung đột
                destroyAllSortables();
                
                // Khởi tạo menu chính (root level)
                const menuStructure = document.getElementById('menu-structure');
                if (menuStructure) {
                    // Tạo sortable cho container chính
                    const rootSortable = new Sortable(menuStructure, {
                        group: 'menu',
                    animation: 150,
                        handle: '.menu-item-handle',
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                        forceFallback: true, // Đảm bảo hiệu ứng kéo thả nhất quán trên các trình duyệt
                        fallbackOnBody: true,
                        fallbackTolerance: 5,
                        swapThreshold: 0.5,
                        direction: 'vertical',
                        emptyInsertThreshold: 10,
                        
                        // Hiệu ứng khi bắt đầu kéo
                    onStart: function(evt) {
                            document.body.classList.add('is-dragging');
                            
                            // Hiển thị chỉ báo thả trong container hiện tại
                            // Tìm container gần nhất
                            const currentContainer = evt.item.closest('.sortable-items') || document.getElementById('menu-structure');
                            const dropIndicator = currentContainer.querySelector('.drop-indicator');
                            
                            if (dropIndicator) {
                                dropIndicator.style.display = 'block';
                            } else {
                                // Nếu không có chỉ báo, chỉ hiển thị một cái ở cuối container
                                const lastDropIndicator = document.querySelector('#menu-structure > .drop-indicator');
                                if (lastDropIndicator) {
                                    lastDropIndicator.style.display = 'block';
                                }
                            }
                            
                            // Tạo chỉ báo quan hệ cha-con nếu chưa có
                            if (!parentIndicator) {
                                parentIndicator = document.createElement('div');
                                parentIndicator.className = 'parent-relationship-indicator';
                                parentIndicator.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v10H5V5z" clip-rule="evenodd" /></svg>Đang tạo quan hệ cha-con';
                                document.body.appendChild(parentIndicator);
                            }
                            parentIndicator.style.display = 'none';
                            
                            // Đánh dấu tất cả các mục menu khác là có thể làm cha
                            const allMenuItems = document.querySelectorAll('.menu-item');
                            const currentItemId = evt.item.dataset.id;
                            
                            allMenuItems.forEach(item => {
                                if (item.dataset.id !== currentItemId && !item.closest(`.children-of-${currentItemId}`)) {
                                    item.classList.add('with-parent-indicator');
                                }
                            });
                        },
                        
                        // Xử lý khi đang kéo
                    onMove: function(evt, originalEvent) {
                            // Nếu không có related element hoặc không phải menu item, bỏ qua
                            if (!evt.related || !evt.related.classList.contains('menu-item')) {
                                if (parentIndicator) parentIndicator.style.display = 'none';
                                return;
                            }
                            
                            // Xác định vùng để tạo menu con - vùng bên phải của menu item
                            const targetRect = evt.related.getBoundingClientRect();
                            const mouseX = originalEvent.clientX;
                            
                            // Vùng bên phải 80px của menu item sẽ được coi là vùng tạo menu con
                            const parentZoneWidth = 80;
                            const isInParentZone = mouseX > (targetRect.right - parentZoneWidth);
                            
                            // Lấy ID của menu item đang kéo và menu item đích
                            const draggedItemId = evt.dragged.dataset.id;
                            const targetItemId = evt.related.dataset.id;
                            
                            // Kiểm tra xem menu item đích có phải là con của menu đang kéo không
                            const isTargetChildOfDragged = evt.related.closest(`.children-of-${draggedItemId}`);
                            
                            // Nếu di chuyển vào vùng tạo menu con và target không phải là con của dragged
                            if (isInParentZone && !isTargetChildOfDragged) {
                                // Hiển thị chỉ báo quan hệ cha-con
                            if (parentIndicator) {
                                parentIndicator.style.display = 'flex';
                                    parentIndicator.style.left = (originalEvent.clientX + 10) + 'px';
                                    parentIndicator.style.top = (originalEvent.clientY + 10) + 'px';
                                    parentIndicator.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5z" /><path d="M11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" /></svg><span>Tạo menu con</span>';
                                    
                                    // Highlight menu item có thể làm cha
                                    evt.related.classList.add('potential-parent');
                                    
                                    // Hiển thị dòng chữ hướng dẫn trong menu item
                                    let dropHint = evt.related.querySelector('.drop-hint');
                                    if (!dropHint) {
                                        dropHint = document.createElement('div');
                                        dropHint.className = 'drop-hint';
                                        dropHint.style.position = 'absolute';
                                        dropHint.style.right = '10px';
                                        dropHint.style.top = '50%';
                                        dropHint.style.transform = 'translateY(-50%)';
                                        dropHint.style.backgroundColor = 'rgba(59, 130, 246, 0.9)';
                                        dropHint.style.color = 'white';
                                        dropHint.style.padding = '2px 8px';
                                        dropHint.style.borderRadius = '4px';
                                        dropHint.style.fontSize = '11px';
                                        dropHint.style.fontWeight = 'bold';
                                        dropHint.style.zIndex = '5';
                                        dropHint.style.pointerEvents = 'none';
                                        dropHint.textContent = 'Thả vào đây →';
                                        evt.related.appendChild(dropHint);
                                    }
                                    
                                    return false; // Không cho phép drop ở đây
                                }
                            } else {
                                // Nếu không ở trong vùng tạo menu con, ẩn chỉ báo
                                if (parentIndicator) {
                            parentIndicator.style.display = 'none';
                                }
                                
                                // Xóa highlight và hint
                            document.querySelectorAll('.potential-parent').forEach(el => {
                                el.classList.remove('potential-parent');
                                    
                                    const dropHint = el.querySelector('.drop-hint');
                                    if (dropHint) {
                                        dropHint.remove();
                                    }
                            });
                        }
                    },
                    
                        // Kết thúc kéo thả
                    onEnd: function(evt) {
                            // Xóa các lớp đánh dấu
                            document.body.classList.remove('is-dragging');
                            
                            // Ẩn tất cả các chỉ báo thả
                            document.querySelectorAll('.drop-indicator').forEach(indicator => {
                                indicator.style.display = 'none';
                            });
                            
                            // Xóa lớp with-parent-indicator
                            document.querySelectorAll('.with-parent-indicator').forEach(item => {
                                item.classList.remove('with-parent-indicator');
                            });
                            
                            // Ẩn chỉ báo quan hệ cha-con
                        if (parentIndicator) {
                            parentIndicator.style.display = 'none';
                        }
                        
                            // Xóa hint
                            document.querySelectorAll('.drop-hint').forEach(hint => {
                                hint.remove();
                            });
                            
                            // Kiểm tra nếu thả vào một mục cha tiềm năng
                            const potentialParent = document.querySelector('.potential-parent');
                            
                            if (potentialParent) {
                                // Nếu có mục cha tiềm năng, thực hiện di chuyển
                                const parentId = potentialParent.dataset.id;
                                const childId = evt.item.dataset.id;
                                
                                // Kiểm tra xem menu item đang kéo có phải là cha của menu tiềm năng không
                                const isPotentialParentChildOfDragged = potentialParent.closest(`.children-of-${childId}`);
                                
                                // Tránh tạo vòng lặp cha-con
                                if (isPotentialParentChildOfDragged) {
                                    console.warn('Không thể tạo quan hệ cha-con vòng lặp!');
                                    potentialParent.classList.remove('potential-parent');
                                    return;
                                }
                                
                                // Nếu đang xử lý, bỏ qua
                                if (processingDrop) {
                                    return;
                                }
                                
                                processingDrop = true;
                                
                                // Tìm hoặc tạo container cho con (hỗ trợ đa cấp)
                                let childrenContainer = potentialParent.querySelector(':scope > .menu-children-container');

                                if (!childrenContainer) {
                                    childrenContainer = document.createElement('div');
                                    childrenContainer.className = 'menu-children-container ml-8 mt-2 border-l-2 border-gray-200 dark:border-gray-600 pl-4';
                                    childrenContainer.setAttribute('data-parent-id', parentId);
                                    potentialParent.appendChild(childrenContainer);
                                }

                                // Di chuyển mục bị kéo vào container con
                                childrenContainer.appendChild(evt.item);

                                // Thêm drop indicator vào cuối container con nếu chưa có
                                const hasDropIndicator = Array.from(childrenContainer.children).some(
                                    child => child.classList.contains('drop-indicator')
                                );

                                if (!hasDropIndicator) {
                                    const dropIndicator = document.createElement('div');
                                    dropIndicator.className = 'drop-indicator hidden p-2 border-2 border-dashed border-blue-300 bg-blue-50 dark:bg-blue-900/20 rounded text-center text-sm text-blue-600 dark:text-blue-400';
                                    dropIndicator.textContent = 'Thả để thêm vào đây';
                                    dropIndicator.style.display = 'none'; // Ẩn mặc định
                                    childrenContainer.appendChild(dropIndicator);
                                }

                                // Khởi tạo lại sortable cho tất cả containers (bao gồm cấp mới)
                                setTimeout(() => {
                                    initSortables();
                                }, 50);
                                
                                // Thu thập cấu trúc menu mới và gửi về server
                                const menuStructure = collectMenuStructure();
                                
                                // Gọi hàm Livewire để cập nhật thứ tự
                                Livewire.dispatch('reorderMenuItem', {
                                    items: menuStructure
                                });
                                
                                // Xóa class potential-parent
                                potentialParent.classList.remove('potential-parent');
                                
                                // Hiển thị thông báo thành công
                                const success = document.createElement('div');
                                success.className = 'fixed top-4 right-4 bg-green-500 text-white py-2 px-4 rounded shadow-lg z-50';
                                success.innerHTML = 'Đã thêm menu con thành công';
                                document.body.appendChild(success);
                                
                                setTimeout(() => {
                                    success.remove();
                                    processingDrop = false;
                                }, 1500);
                                
                            return;
                        }
                        
                            // Nếu chỉ kéo thả trong cùng một cấp, cập nhật thứ tự
                            // Chờ một chút để đảm bảo DOM đã cập nhật
                            setTimeout(() => {
                                const menuStructure = collectMenuStructure();
                                
                                // Gọi hàm Livewire để cập nhật thứ tự
                                Livewire.dispatch('reorderMenuItem', {
                                    items: menuStructure
                                });
                            }, 100);
                        }
                    });
                    
                    sortableInstances.push(rootSortable);
                }
                
                // Khởi tạo các sortable cho các container con
                const childContainers = document.querySelectorAll('.menu-children-container');
                childContainers.forEach(container => {
                    const childSortable = new Sortable(container, {
                        group: 'menu',
                        animation: 150,
                        handle: '.menu-item-handle',
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        dragClass: 'sortable-drag',
                        forceFallback: true,
                        fallbackOnBody: true,
                        fallbackTolerance: 5,
                        swapThreshold: 0.5,
                        direction: 'vertical',
                        onEnd: function() {
                            // Khi kéo thả xong, cập nhật cấu trúc menu
                            setTimeout(() => {
                                const menuStructure = collectMenuStructure();
                                
                                // Gọi hàm Livewire để cập nhật thứ tự
                                Livewire.dispatch('reorderMenuItem', {
                                    items: menuStructure
                                });
                            }, 100);
                        }
                    });
                    
                    sortableInstances.push(childSortable);
                });
                
                console.log('All sortables initialized successfully');
            }
            
            // Hàm thu thập cấu trúc menu hiện tại (hỗ trợ đa cấp)
            function collectMenuStructure() {
                const menuStructure = [];

                // Hàm đệ quy để xử lý menu items ở mọi cấp
                function processMenuItems(container, parentId = null) {
                    const items = [];
                    const menuItems = container.querySelectorAll(':scope > .wordpress-menu-item, :scope > .menu-item');

                    menuItems.forEach((item, index) => {
                        const menuItem = {
                            id: parseInt(item.dataset.id),
                            order: index,
                            parent_id: parentId
                        };

                        // Kiểm tra xem có menu con không
                        const childrenContainer = item.querySelector('.menu-children-container');
                        if (childrenContainer) {
                            // Đệ quy xử lý các menu con
                            const childItems = processMenuItems(childrenContainer, menuItem.id);
                            if (childItems.length > 0) {
                                menuItem.children = childItems;
                            }
                        }

                        items.push(menuItem);
                    });

                    return items;
                }

                // Bắt đầu từ container gốc
                const rootContainer = document.getElementById('menu-structure');
                if (rootContainer) {
                    return processMenuItems(rootContainer);
                }

                return menuStructure;
            }
        </script>

        <style>
            /* WordPress-style Menu Manager */
            .wordpress-menu-manager {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            }

            /* Two Column Layout */
            @media (min-width: 1024px) {
                .wordpress-menu-manager .lg\\:w-96 {
                    width: 384px;
                    max-width: 384px;
                    position: sticky;
                    top: 20px;
                    align-self: flex-start;
                }

                .wordpress-menu-manager .flex-1 {
                    min-width: 0;
                    flex: 1;
                }
            }

            /* WordPress-style Menu Structure */
            .menu-structure {
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 4px;
                min-height: 400px;
                padding: 12px;
                position: relative;
                box-shadow: 0 1px 1px rgba(0,0,0,.04);
            }

            .dark .menu-structure {
                background: #1f2937;
                border-color: #374151;
            }

            /* WordPress-style Menu Item */
            .menu-item {
                background: #f7f7f7;
                border: 1px solid #dfdfdf;
                border-radius: 0;
                margin: 0 0 -1px 0;
                position: relative;
                transition: all 0.15s ease-in-out;
                cursor: move;
            }

            .dark .menu-item {
                background: #2d3748;
                border-color: #4a5568;
            }

            .menu-item:hover {
                background: #f1f1f1;
                border-color: #999;
                z-index: 10;
            }

            .dark .menu-item:hover {
                background: #374151;
                border-color: #6b7280;
            }

            .menu-item:first-child {
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }

            .menu-item:last-child {
                border-bottom-left-radius: 4px;
                border-bottom-right-radius: 4px;
                margin-bottom: 0;
            }

            .menu-item-content {
                padding: 12px 18px;
                display: flex;
                align-items: center;
                position: relative;
                min-height: 40px;
            }

            .menu-item-handle {
                width: 20px;
                height: 20px;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNSIgY3k9IjUiIHI9IjEuNSIgZmlsbD0iIzk5OSIvPgo8Y2lyY2xlIGN4PSIxNSIgY3k9IjUiIHI9IjEuNSIgZmlsbD0iIzk5OSIvPgo8Y2lyY2xlIGN4PSI1IiBjeT0iMTAiIHI9IjEuNSIgZmlsbD0iIzk5OSIvPgo8Y2lyY2xlIGN4PSIxNSIgY3k9IjEwIiByPSIxLjUiIGZpbGw9IiM5OTkiLz4KPGNpcmNsZSBjeD0iNSIgY3k9IjE1IiByPSIxLjUiIGZpbGw9IiM5OTkiLz4KPGNpcmNsZSBjeD0iMTUiIGN5PSIxNSIgcj0iMS41IiBmaWxsPSIjOTk5Ii8+Cjwvc3ZnPgo=') no-repeat center;
                margin-right: 14px;
                cursor: move;
                opacity: 0.6;
                flex-shrink: 0;
            }

            .menu-item-handle:hover {
                opacity: 1;
            }

            .menu-item-title {
                font-weight: 400;
                color: #333;
                flex: 1;
                font-size: 13px;
                line-height: 1.4;
                margin: 0;
            }

            .dark .menu-item-title {
                color: #e2e8f0;
            }

            .menu-item-actions {
                display: flex;
                gap: 8px;
                align-items: center;
                margin-left: 14px;
                opacity: 0.7;
                transition: opacity 0.2s ease;
            }

            .menu-item:hover .menu-item-actions {
                opacity: 1;
            }

            .menu-item-actions button {
                background: none;
                border: none;
                color: #666;
                cursor: pointer;
                padding: 2px 6px;
                border-radius: 2px;
                font-size: 11px;
                text-decoration: none;
                line-height: 1;
            }

            .menu-item-actions button:hover {
                background: #0073aa;
                color: white;
            }

            /* WordPress-style Nested Items (hỗ trợ đa cấp) */
            .menu-children-container {
                position: relative;
            }

            .menu-children-container::before {
                content: '';
                position: absolute;
                left: -2px;
                top: 0;
                bottom: 0;
                width: 2px;
                background: #dfdfdf;
            }

            .dark .menu-children-container::before {
                background: #4a5568;
            }

            /* Indentation cho các cấp menu */
            .menu-children-container .wordpress-menu-item {
                position: relative;
            }

            .menu-children-container .wordpress-menu-item::before {
                content: '';
                position: absolute;
                left: -32px;
                top: 50%;
                width: 20px;
                height: 1px;
                background: #dfdfdf;
                transform: translateY(-50%);
            }

            .dark .menu-children-container .wordpress-menu-item::before {
                background: #4a5568;
            }

            /* Cấp 1 */
            .menu-children-container {
                margin-left: 32px;
                padding-left: 16px;
            }

            /* Cấp 2 */
            .menu-children-container .menu-children-container {
                margin-left: 24px;
                padding-left: 12px;
            }

            /* Cấp 3 và cao hơn */
            .menu-children-container .menu-children-container .menu-children-container {
                margin-left: 20px;
                padding-left: 10px;
            }

            /* Visual indicator cho depth */
            .menu-children-container .wordpress-menu-item {
                border-left: 2px solid transparent;
                transition: border-color 0.2s ease;
            }

            .menu-children-container .wordpress-menu-item:hover {
                border-left-color: #3b82f6;
            }
            /* WordPress-style Sortable Effects */
            .sortable-ghost {
                opacity: 0.5;
                background: #e1f5fe !important;
                border: 1px dashed #0073aa !important;
                transform: rotate(0deg) !important;
            }

            .sortable-chosen {
                background: #fff !important;
                border-color: #0073aa !important;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2) !important;
                z-index: 1000;
                transform: rotate(2deg);
            }

            .sortable-drag {
                opacity: 0.9;
                transform: rotate(2deg);
                box-shadow: 0 5px 15px rgba(0,0,0,0.3) !important;
                border-color: #0073aa !important;
            }

            /* WordPress-style Drop Zones */
            .drop-zone {
                height: 2px;
                background: transparent;
                margin: 1px 0;
                transition: all 0.2s ease;
                position: relative;
            }

            .drop-zone.drag-over {
                height: 20px;
                background: #e1f5fe;
                border: 1px dashed #0073aa;
                border-radius: 2px;
                margin: 5px 0;
            }

            .drop-zone.drag-over::after {
                content: 'Drop here';
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                font-size: 11px;
                color: #0073aa;
                font-weight: 500;
            }

            /* WordPress-style Drag Feedback */
            .is-dragging {
                user-select: none;
            }

            .is-dragging .menu-item:not(.sortable-chosen) {
                opacity: 0.7;
            }

            .is-dragging .menu-structure {
                background: #fafafa;
            }

            .dark.is-dragging .menu-structure {
                background: #1a202c;
            }

            /* WordPress-style Empty State */
            .menu-structure-empty {
                text-align: center;
                padding: 40px 20px;
                color: #666;
                font-style: italic;
                border: 2px dashed #ddd;
                border-radius: 4px;
                background: #fafafa;
            }

            .dark .menu-structure-empty {
                color: #a0aec0;
                border-color: #4a5568;
                background: #2d3748;
            }
            
            /* Cải thiện Drag Handle */
            .menu-item-handle {
                cursor: grab !important;
                user-select: none;
                padding: 4px;
                border-radius: 4px;
                transition: background-color 0.2s;
            }
            
            .menu-item-handle:hover {
                background-color: rgba(59, 130, 246, 0.1);
            }
            
            .menu-item-handle:active {
                cursor: grabbing !important;
                background-color: rgba(59, 130, 246, 0.2);
            }
            
            .sort-handle {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                padding: 0.25rem;
                color: #6b7280;
                border-radius: 0.25rem;
            }
            
            .menu-item-handle:hover .sort-handle {
                color: #3b82f6;
            }
            
            /* Drop Indicators - Chỉ báo vùng thả */
            .drop-indicator {
                display: none;
                margin: 0.5rem 0;
                padding: 0.75rem;
                border: 2px dashed #3b82f6;
                border-radius: 0.375rem;
                background-color: rgba(219, 234, 254, 0.5);
                color: #3b82f6;
                text-align: center;
                font-size: 0.875rem;
                transition: all 0.15s ease;
                font-weight: 500;
            }
            
            /* Container Menu Con */
            .menu-children-container {
                margin-left: 2rem;
                border-left: 4px solid #3b82f6;
                padding-left: 1.25rem;
                padding-top: 0.5rem;
                padding-bottom: 0.5rem;
                margin-bottom: 8px;
                min-height: 20px; /* Tăng chiều cao tối thiểu */
                transition: all 0.15s ease;
                background-color: rgba(243, 244, 246, 0.4);
                border-radius: 0 0 0.5rem 0;
                will-change: transform;
                position: relative;
            }
            
            .menu-children-container::before {
                content: 'Menu con';
                display: block;
                font-size: 11px;
                color: #3b82f6;
                margin-bottom: 6px;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            
            /* Hiệu ứng kéo thả */
            .sortable-ghost {
                opacity: 0.4 !important;
                background-color: #e5e7eb !important;
                border: 2px dashed #3b82f6 !important;
                border-radius: 0.375rem;
                height: 3rem;
                overflow: hidden;
            }
            
            .sortable-chosen {
                background-color: #f8fafc;
                box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.15);
                z-index: 10;
                will-change: transform;
                border-left-color: #3b82f6;
            }
            
            .sortable-drag {
                opacity: 0.9;
                z-index: 20;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
                border: 2px solid #3b82f6 !important;
                background-color: #fff !important;
                will-change: transform;
            }
            
            /* Chỉ báo đang kéo */
            body.is-dragging * {
                cursor: grabbing !important;
            }
            
            body.is-dragging .menu-item:not(.sortable-chosen):not(.sortable-ghost):hover {
                border-left-color: #3b82f6;
                background-color: rgba(239, 246, 255, 0.7);
            }
            
            /* Kiểu dark mode */
            .dark .potential-parent {
                border: 3px solid #60a5fa !important;
                background-color: rgba(29, 78, 216, 0.4) !important;
                box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.6), 0 4px 8px rgba(37, 99, 235, 0.3);
            }
            
            .dark .menu-children-container {
                background-color: rgba(30, 41, 59, 0.4);
            }
            
            .dark .menu-children-container::before {
                color: #60a5fa;
            }
            
            .dark .with-parent-indicator::before {
                background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.15));
            }
            
            .dark .with-parent-indicator:hover::before {
                background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.3));
                border-right: 3px dashed rgba(96, 165, 250, 0.8);
            }
            
            .dark .with-parent-indicator::after {
                color: rgba(96, 165, 250, 0.8);
            }
            
            .dark .drop-indicator {
                border-color: #60a5fa;
                background-color: rgba(37, 99, 235, 0.2);
                color: #60a5fa;
            }
            
            /* Nút lưu thay đổi */
            .save-changes-button {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 100;
                padding: 10px 20px;
                background-color: #3b82f6;
                color: white;
                border-radius: 8px;
                box-shadow: 0 4px 10px rgba(0,0,0,0.2);
                display: flex;
                align-items: center;
                gap: 6px;
                font-weight: 500;
                transition: all 0.2s ease;
                border: none;
                outline: none;
                cursor: pointer;
            }
            
            .save-changes-button:hover {
                background-color: #2563eb;
                box-shadow: 0 6px 12px rgba(0,0,0,0.25);
                transform: translateY(-2px);
            }
            
            .save-changes-button:active {
                transform: translateY(1px);
                box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            }
            
            .dark .save-changes-button {
                background-color: #3b82f6;
                color: white;
            }
            
            .dark .save-changes-button:hover {
                background-color: #2563eb;
            }
        </style>

        <!-- Modal xóa hàng loạt -->
        <x-filament::modal
            id="bulk-delete-menu-items"
            icon="heroicon-o-exclamation-triangle"
            icon-color="danger"
            heading="Xóa các mục menu đã chọn"
            width="md"
        >
            <div class="py-2">
                <p class="text-gray-700 dark:text-gray-300">
                    Bạn có chắc chắn muốn xóa {{ count($this->selectedMenuItems) }} mục menu đã chọn?
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                    Hành động này không thể hoàn tác.
                </p>
            </div>
            
            <x-slot name="footerActions">
                <x-filament::button
                    x-on:click="$dispatch('close-modal', { id: 'bulk-delete-menu-items' })"
                    color="gray"
                >
                    Hủy
                </x-filament::button>
                
                <x-filament::button
                    wire:click="deleteBulkMenuItems"
                    color="danger"
                >
                    Xác nhận xóa
                </x-filament::button>
            </x-slot>
        </x-filament::modal>
    </div>

    <script>
        // Listen for menu items updated event
        document.addEventListener('livewire:init', () => {
            Livewire.on('menuItemsUpdated', () => {
                // Force refresh the Livewire component
                @this.$refresh();
            });
        });
    </script>
</x-filament-panels::page>