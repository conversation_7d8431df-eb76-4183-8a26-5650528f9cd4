@extends('templates.auvista.layouts.default')

@section('title', __('messages.about_us') . ' - Auvista')

@php
    $breadcrumb = false;
@endphp

@section('content')
    <main id="main">
        <div class="page-wrapper page-about">
            <div class="relative z-10">
                <!-- Hero Section -->
                <div class="bg-gradient8 pt-16 lg:pt-[86px] pb-16 lg:pb-[86px] text-white">

                    <div class="container mx-auto">
                        <div class="grid grid-cols-1 lg:grid-cols-2 items-center gap-4 lg:gap-[30px]">
                            <div class="col-span-1">
                                <div class="rounded-3xl overflow-hidden">
                                    <img src="{{ asset('images/image-about.png') }}" alt="image-about"
                                        class="w-full h-auto" />
                                </div>
                            </div>
                            <div class="col-span-1 lg:-order-1">
                                <h1 class="font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-6">
                                    {{ __('messages.about_us') }}
                                </h1>
                                <div class="lg:text-xl leading-[1.4] space-y-5">
                                    <p>
                                        {{ __('messages.about_description_1') }}
                                    </p>
                                    <p>
                                        {{ __('messages.about_description_2') }}
                                    </p>
                                    <p>
                                        {{ __('messages.about_description_3') }}
                                    </p>
                                </div>
                                <div class="mt-[30px]">
                                    <a href="{{ route('static.page', 'san-pham') }}"
                                        class="text-secondary-main bg-white rounded-full px-[30px] py-2 h-11 font-bold leading-[1.4]">
                                        <span>{{ __('messages.explore_products') }}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="relative z-50" style="z-index: 999;">
                    <img src="../images/wave.svg" alt="wave" class="w-full h-auto relative z-50"
                        style="z-index: 999; position: relative;" />
                </div>
                <!-- Vision/Mission Section -->
                <div class="relative -mt-20 pt-40 pb-16 md:pb-24 xl:pb-[130px] bg-img2 bg-cover bg-no-repeat bg-center text-white overflow-hidden"
                    style="background-image: url('{{ asset('images/bg-about-section-2.png') }}');">
                    <div class="container mx-auto relative z-10">
                        <div class="flex flex-col items-center justify-center relative">
                            <!-- Centered Logo with Wave Background -->
                            <div
                                class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex items-center justify-center mb-12 md:mb-20" style="top: 50% !important;">
                                <div class="ripple-container">
                                    <!-- Hiệu ứng sóng gợn -->
                                    <div class="ripple"></div>
                                    <div class="ripple"></div>
                                    <div class="ripple"></div>
                                    <div class="ripple"></div>
                                    <div class="ripple"></div>
                                    <div class="ripple"></div>

                                    <!-- Hiệu ứng pulse ring -->
                                    <div class="pulse-ring"></div>

                                    <!-- Hiệu ứng sóng âm thanh -->
                                    <div class="sound-waves">
                                        <div class="wave-line"></div>
                                        <div class="wave-line"></div>
                                        <div class="wave-line"></div>
                                        <div class="wave-line"></div>
                                    </div>

                                    <!-- Hiệu ứng particles -->
                                    <div class="floating-particles">
                                        <div class="particle"></div>
                                        <div class="particle"></div>
                                        <div class="particle"></div>
                                        <div class="particle"></div>
                                    </div>

                                    <!-- Hiệu ứng glow -->
                                    <div class="glow-effect"></div>

                                    <!-- Logo trung tâm -->
                                    <div class="logo-center">
                                        <!-- Logo on top -->
                                        <img src="{{ asset('images/center-image.png') }}" alt="Auvista Logo"
                                            class="relative z-10" />
                                    </div>
                                </div>
                            </div>
                            <!-- 4 Info Blocks -->
                            <div class="flex justify-between w-full max-w-[1170px] pb-14 md:pb-24 lg:pb-40 xl:pb-64">
                                <!-- Top Row -->
                                <div class="w-full max-w-[286px] text-center space-y-4">
                                    <div class="flex justify-center mb-4">
                                        <svg width="50" height="50" viewBox="0 0 50 50" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <g clip-path="url(#clip0_3_5097)">
                                                <path
                                                    d="M28.7382 11.3899C27.5268 11.0579 26.269 10.8895 25 10.8895C17.2194 10.8895 10.8896 17.2194 10.8896 25C10.8896 32.7806 17.2194 39.1104 25 39.1104C32.7806 39.1104 39.1104 32.7806 39.1104 25C39.1104 23.7309 38.9421 22.4732 38.6101 21.2618C38.3819 20.4296 38.8717 19.57 39.7039 19.3419C40.5356 19.1139 41.3957 19.6035 41.6238 20.4357C42.0296 21.9163 42.2354 23.4519 42.2354 25C42.2354 34.5036 34.5036 42.2354 25 42.2354C15.4964 42.2354 7.76455 34.5036 7.76455 25C7.76455 15.4964 15.4964 7.76454 25 7.76454C26.5481 7.76454 28.0837 7.97031 29.5643 8.37617C30.2583 8.5664 30.7142 9.19589 30.7141 9.88242C30.7141 10.019 30.6959 10.1579 30.6581 10.2961C30.4301 11.1283 29.5706 11.6184 28.7382 11.3899ZM47.033 16.8398C46.2056 17.0849 45.7333 17.9541 45.9783 18.7816C46.5733 20.7914 46.875 22.8835 46.875 25C46.875 37.0619 37.0619 46.875 25 46.875C12.9381 46.875 3.125 37.0619 3.125 25C3.125 12.9381 12.9381 3.12499 25 3.12499C27.1166 3.12499 29.2087 3.42665 31.2181 4.02158C32.0456 4.26669 32.9148 3.79423 33.1599 2.96689C33.2036 2.81894 33.2245 2.66982 33.2245 2.52304C33.2247 1.84872 32.7848 1.22636 32.1052 1.02509C29.8075 0.344818 27.4171 -6.3024e-06 25 -6.3024e-06C18.3223 -6.3024e-06 12.0441 2.60048 7.32227 7.32226C2.60049 12.0442 0 18.3223 0 25C0 31.6777 2.60049 37.9559 7.32227 42.6777C12.044 47.3996 18.3223 50 25 50C31.6777 50 37.9558 47.3995 42.6777 42.6777C47.3995 37.9559 50 31.6777 50 25C50 22.5831 49.6551 20.1925 48.9748 17.8945C48.7299 17.0671 47.8604 16.5952 47.033 16.8398ZM25 18.75C25.2298 18.75 25.4618 18.7627 25.6897 18.7877C26.5479 18.8821 27.3193 18.2627 27.4133 17.405C27.5074 16.5472 26.8884 15.7755 26.0306 15.6814C25.6897 15.6439 25.3432 15.625 25 15.625C19.8306 15.625 15.625 19.8306 15.625 25C15.625 30.1694 19.8306 34.375 25 34.375C30.1694 34.375 34.375 30.1694 34.375 25C34.375 24.137 33.6755 23.4375 32.8125 23.4375C31.9495 23.4375 31.25 24.137 31.25 25C31.25 28.4463 28.4463 31.25 25 31.25C21.5537 31.25 18.75 28.4463 18.75 25C18.75 21.5537 21.5537 18.75 25 18.75ZM32.9779 14.8126L33.7563 7.00527C33.792 6.64609 33.951 6.31054 34.2062 6.05536L39.8041 0.457611C40.2354 0.0264585 40.8777 -0.114459 41.4499 0.0965757C42.0219 0.307611 42.4189 0.83222 42.4667 1.44013L42.9105 7.08954L48.5599 7.53329C49.1678 7.58105 49.6924 7.97812 49.9034 8.55009C50.1145 9.12216 49.9735 9.76474 49.5424 10.1959L43.9445 15.7936C43.6895 16.0488 43.3538 16.2078 42.9947 16.2435L35.1876 17.0223L26.1049 26.1049C25.7997 26.41 25.3999 26.5625 25 26.5625C24.6 26.5625 24.2003 26.41 23.8952 26.1048C23.285 25.4946 23.285 24.5053 23.8952 23.8951L32.9779 14.8126ZM36.2215 13.7787L42.1293 13.1894L44.9354 10.3833L41.3351 10.1005C40.5687 10.0402 39.96 9.43144 39.8997 8.66513L39.6169 5.06445L36.8105 7.8707L36.2215 13.7787Z"
                                                    fill="white" />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_3_5097">
                                                    <rect width="50" height="50" fill="white" />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                    </div>
                                    <h3 class="font-medium text-xl md:text-2xl leading-[1.4] tracking-wide mb-3">
                                        {{ __('messages.vision') }}
                                    </h3>
                                    <p class="text-base leading-[1.4]">
                                        {{ __('messages.vision_description') }}
                                    </p>
                                </div>
                                <div class="w-full max-w-[286px] text-center space-y-4">
                                    <div class="flex justify-center mb-4">
                                        <svg width="50" height="50" viewBox="0 0 50 50" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <g clip-path="url(#clip0_3_5102)">
                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                    d="M29.1688 1.6129H1.71855C1.6624 1.6129 1.6129 1.65393 1.6129 1.70948V44.5771C1.6129 44.6316 1.66321 44.6704 1.71855 44.6704H23.9277C24.3728 44.6704 24.7342 45.0318 24.7342 45.4768C24.7342 45.922 24.3728 46.2833 23.9277 46.2833H1.71855C0.767137 46.2833 0 45.5128 0 44.5771V1.70948C0 0.768347 0.767944 0 1.71855 0H29.6003C30.0712 0 30.4785 0.16996 30.8095 0.497581L35.9169 5.55464C36.2509 5.88528 36.4229 6.29274 36.4229 6.76321V21.3243C36.4229 21.7694 36.0615 22.1307 35.6164 22.1307C35.1714 22.1307 34.81 21.7694 34.81 21.3243V7.19839H30.4312C29.737 7.19839 29.1688 6.62772 29.1688 5.94042V1.6129ZM33.6557 5.58548L30.7817 2.73982V5.58548H33.6557ZM17.37 8.7873V19.4932C17.37 19.9387 17.0089 20.2997 16.5635 20.2997H5.75061C5.30524 20.2997 4.94415 19.9387 4.94415 19.4932V8.7873C4.94415 8.34184 5.30524 7.98085 5.75061 7.98085H16.5635C17.0089 7.98085 17.37 8.34184 17.37 8.7873ZM15.7571 9.59375H6.55706V18.6868H15.7571V9.59375ZM20.3567 11.4716C19.9116 11.4716 19.5502 11.1102 19.5502 10.6651C19.5502 10.22 19.9116 9.85867 20.3567 9.85867H27.758C28.203 9.85867 28.5644 10.22 28.5644 10.6651C28.5644 11.1102 28.203 11.4716 27.758 11.4716H20.3567ZM20.3567 14.9467C19.9116 14.9467 19.5502 14.5854 19.5502 14.1402C19.5502 13.6952 19.9116 13.3338 20.3567 13.3338H30.6723C31.1173 13.3338 31.4787 13.6952 31.4787 14.1402C31.4787 14.5854 31.1173 14.9467 30.6723 14.9467H20.3567ZM20.3567 18.4219C19.9116 18.4219 19.5502 18.0605 19.5502 17.6154C19.5502 17.1704 19.9116 16.809 20.3567 16.809H30.6723C31.1173 16.809 31.4787 17.1704 31.4787 17.6154C31.4787 18.0605 31.1173 18.4219 30.6723 18.4219H20.3567ZM5.75061 24.4018C5.30554 24.4018 4.94415 24.0405 4.94415 23.5954C4.94415 23.1503 5.30554 22.7889 5.75061 22.7889H30.6723C31.1173 22.7889 31.4787 23.1503 31.4787 23.5954C31.4787 24.0405 31.1173 24.4018 30.6723 24.4018H5.75061ZM5.75061 27.877C5.30554 27.877 4.94415 27.5156 4.94415 27.0706C4.94415 26.6255 5.30554 26.2641 5.75061 26.2641H23.9643C24.4094 26.2641 24.7708 26.6255 24.7708 27.0706C24.7708 27.5156 24.4094 27.877 23.9643 27.877H5.75061ZM5.75061 31.3522C5.30554 31.3522 4.94415 30.9908 4.94415 30.5458C4.94415 30.1006 5.30554 29.7393 5.75061 29.7393H22.9267C23.3718 29.7393 23.7332 30.1006 23.7332 30.5458C23.7332 30.9908 23.3718 31.3522 22.9267 31.3522H5.75061ZM5.75061 34.8273C5.30554 34.8273 4.94415 34.466 4.94415 34.0209C4.94415 33.5758 5.30554 33.2144 5.75061 33.2144H20.8308C21.2759 33.2144 21.6373 33.5758 21.6373 34.0209C21.6373 34.466 21.2759 34.8273 20.8308 34.8273H5.75061ZM5.75061 38.3025C5.30554 38.3025 4.94415 37.9411 4.94415 37.4961C4.94415 37.051 5.30554 36.6896 5.75061 36.6896H20.1703C20.6153 36.6896 20.9767 37.051 20.9767 37.4961C20.9767 37.9411 20.6153 38.3025 20.1703 38.3025H5.75061ZM39.5155 24.9688C40.4427 25.2253 41.3341 25.5946 42.1721 26.0697L43.8772 25.3115C44.1822 25.1759 44.5391 25.2421 44.7751 25.4781L47.2309 27.934C47.4666 28.1697 47.5331 28.5261 47.3981 28.8308L46.6426 30.5363C47.118 31.375 47.4876 32.2672 47.7443 33.1975L49.4832 33.8667C49.7946 33.9866 50 34.2857 50 34.6194V38.0933C50 38.4268 49.7946 38.726 49.4832 38.8459L47.7439 39.5152C47.4868 40.4425 47.1156 41.3338 46.6422 42.1718L47.3981 43.8782C47.5331 44.1831 47.4666 44.5394 47.2309 44.7751L44.7751 47.2309C44.5394 47.4666 44.1831 47.5331 43.8782 47.3981L42.1733 46.6428C41.3367 47.1179 40.4432 47.4874 39.5152 47.744L38.8459 49.4832C38.726 49.7946 38.4268 50 38.0933 50H34.6194C34.2857 50 33.9866 49.7946 33.8667 49.4832L33.1975 47.7442C32.2675 47.4871 31.3756 47.1158 30.5373 46.6422L28.8308 47.3981C28.5261 47.5331 28.1697 47.4666 27.934 47.2309L25.4781 44.7751C25.2421 44.5391 25.1759 44.1822 25.3115 43.8772L26.0695 42.1727C25.5945 41.3364 25.225 40.4433 24.9663 39.5157L23.2259 38.8459C22.9145 38.726 22.7091 38.4268 22.7091 38.0933V34.6194C22.7091 34.2857 22.9145 33.9866 23.2259 33.8667L24.966 33.197C25.2247 32.2671 25.5944 31.3753 26.0697 30.537L25.3115 28.8319C25.1759 28.5269 25.2421 28.17 25.4781 27.934L27.934 25.4781C28.17 25.2421 28.5269 25.1759 28.8319 25.3115L30.5369 26.0697C31.3752 25.5945 32.2667 25.2251 33.1943 24.9685L33.8669 23.2252C33.987 22.9142 34.286 22.7091 34.6194 22.7091H38.0933C38.427 22.7091 38.7263 22.9147 38.8461 23.2263L39.5155 24.9688ZM38.1591 25.9353L37.5392 24.322H35.1726L34.5496 25.9364C34.4539 26.1843 34.2422 26.3692 33.9837 26.4306C32.9339 26.6801 31.9326 27.095 31.0123 27.662C30.7857 27.8016 30.5048 27.8205 30.2616 27.7123L28.6828 27.0103L27.0103 28.6828L27.7123 30.2616C27.8205 30.5048 27.8016 30.7857 27.662 31.0123C27.0946 31.9332 26.6796 32.9352 26.4267 33.9889C26.3648 34.247 26.1798 34.4581 25.9323 34.5533L24.322 35.1731V37.5395L25.9323 38.1592C26.1797 38.2544 26.3645 38.4653 26.4266 38.7231C26.6794 39.7734 27.0941 40.7783 27.6613 41.6957C27.8015 41.9225 27.8207 42.2038 27.7123 42.4475L27.0103 44.0263L28.6834 45.6994L30.2626 44.9998C30.5049 44.8924 30.7846 44.9111 31.0106 45.0495C31.9312 45.6136 32.933 46.0322 33.9867 46.2819C34.2457 46.3431 34.4578 46.5284 34.5533 46.7769L35.1731 48.3871H37.5395L38.1592 46.7769C38.2547 46.5286 38.4666 46.3434 38.7254 46.282C39.7749 46.0326 40.779 45.6181 41.6957 45.0512C41.9222 44.9113 42.203 44.892 42.4465 44.9998L44.0257 45.6994L45.6994 44.0257L44.9998 42.4465C44.8924 42.2041 44.9111 41.9245 45.0495 41.6986C45.6137 40.7777 46.0323 39.7759 46.282 38.7254C46.3434 38.4666 46.5286 38.2547 46.7769 38.1592L48.3871 37.5395V35.1731L46.7769 34.5533C46.5284 34.4578 46.3431 34.2457 46.2819 33.9867C46.0324 32.9337 45.6176 31.9325 45.0506 31.0123C44.9112 30.7861 44.8921 30.5056 44.9998 30.2626L45.6994 28.6834L44.0263 27.0103L42.4475 27.7123C42.2043 27.8205 41.9234 27.8016 41.6968 27.662C40.7765 27.095 39.7753 26.6801 38.7254 26.4306C38.4664 26.3692 38.2545 26.1838 38.1591 25.9353ZM36.3543 28.9329C40.4549 28.9329 43.7797 32.2538 43.7797 36.3543C43.7797 40.4548 40.4548 43.7797 36.3543 43.7797C32.2538 43.7797 28.9329 40.4549 28.9329 36.3543C28.9329 32.2537 32.2537 28.9329 36.3543 28.9329ZM36.3543 30.5458C33.1449 30.5458 30.5458 33.1449 30.5458 36.3543C30.5458 39.564 33.1448 42.1668 36.3543 42.1668C39.5641 42.1668 42.1668 39.5641 42.1668 36.3543C42.1668 33.1448 39.564 30.5458 36.3543 30.5458ZM33.021 36.441C32.7062 36.1263 32.7062 35.6153 33.021 35.3006C33.3357 34.9859 33.8468 34.9859 34.1615 35.3006L35.756 36.895L38.5476 34.1033C38.8623 33.7886 39.3734 33.7886 39.6881 34.1033C40.0028 34.418 40.0028 34.9291 39.6881 35.2439L36.3262 38.6057C36.0113 38.9207 35.5006 38.9207 35.1857 38.6057L33.021 36.441Z"
                                                    fill="white" />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_3_5102">
                                                    <rect width="50" height="50" fill="white" />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                    </div>
                                    <h3 class="font-medium text-xl md:text-2xl leading-[1.4] tracking-wide mb-3">
                                        {{ __('messages.mission') }}
                                    </h3>
                                    <p class="text-base leading-[1.4]">
                                        {{ __('messages.mission_description') }}
                                    </p>
                                </div>
                            </div>
                            <div class="flex justify-between w-full max-w-[1170px]">
                                <!-- Bottom Row -->
                                <div class="w-full max-w-[286px] text-center space-y-4">
                                    <div class="flex justify-center mb-4">
                                        <svg width="50" height="50" viewBox="0 0 50 50" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <g clip-path="url(#clip0_3_5107)">
                                                <path
                                                    d="M48.763 20.416L42.7873 19.4595C42.4497 18.3727 42.0129 17.3191 41.4815 16.3106L45.0316 11.4075C45.4392 10.8444 45.3775 10.069 44.886 9.57738L40.3774 5.06894C39.8859 4.57768 39.1103 4.51562 38.5475 4.92358L33.6445 8.47322C32.6356 7.94165 31.5819 7.50468 30.4955 7.16728L29.539 1.19197C29.4292 0.505668 28.8372 0.000976562 28.1422 0.000976562H21.7661C21.0712 0.000976562 20.4791 0.505668 20.3693 1.19197L19.4128 7.16728C18.326 7.50468 17.2724 7.94165 16.2635 8.47322L11.3607 4.92358C10.7976 4.51562 10.0222 4.57768 9.53079 5.06894L5.02215 9.57738C4.53072 10.0688 4.469 10.8444 4.87661 11.4075L8.42661 16.3106C7.89522 17.3191 7.45825 18.3731 7.12102 19.4595L1.14534 20.416C0.459212 20.5259 -0.0456543 21.1181 -0.0456543 21.8129V28.1889C-0.0456543 28.8839 0.459212 29.4759 1.14534 29.5857L7.12102 30.5422C7.45843 31.6289 7.89539 32.6827 8.42679 33.6912L4.87678 38.5943C4.46917 39.1572 4.53089 39.9328 5.02232 40.4244L9.53096 44.933C10.0224 45.4243 10.798 45.4863 11.3609 45.0784L16.2639 41.5286C17.2725 42.0603 18.3265 42.4973 19.413 42.8347L20.3695 48.81C20.4793 49.4963 21.0714 50.001 21.7663 50.001H28.1424C28.8373 50.001 29.4294 49.4963 29.5392 48.81L30.4957 42.8343C31.5826 42.4967 32.6362 42.0598 33.6447 41.5284L38.5476 45.0784C39.1107 45.486 39.8863 45.4243 40.3777 44.933L44.8864 40.4244C45.3778 39.933 45.4395 39.1574 45.0319 38.5943L41.4821 33.6913C42.0137 32.6827 42.4508 31.6287 42.788 30.5422L48.7634 29.5857C49.4495 29.4759 49.9543 28.8837 49.9543 28.1889V21.8129C49.954 21.1181 49.4491 20.5259 48.763 20.416ZM47.1246 26.9827L41.449 27.8913C40.8923 27.9804 40.4417 28.3916 40.302 28.9378C39.9316 30.3868 39.3553 31.7764 38.5892 33.0678C38.3017 33.5529 38.3294 34.1623 38.6601 34.619L42.0322 39.2765L39.2295 42.0792L34.5722 38.7071C34.1152 38.3763 33.5058 38.3487 33.0208 38.6362C31.73 39.4017 30.3405 39.9781 28.8906 40.3491C28.3445 40.4888 27.9333 40.9393 27.8442 41.4958L26.9356 47.1718H22.972L22.0636 41.4962C21.9745 40.9397 21.5633 40.4889 21.0171 40.3492C19.5681 39.9788 18.1785 39.4025 16.887 38.6364C16.402 38.3489 15.7924 38.3766 15.3358 38.7073L10.6785 42.0792L7.87577 39.2765L11.248 34.619C11.5787 34.1624 11.6065 33.5529 11.319 33.068C10.5533 31.7767 9.97712 30.3872 9.60629 28.9376C9.46659 28.3914 9.01601 27.9804 8.45933 27.8913L2.78338 26.9827V23.0193L8.45933 22.1107C9.01601 22.0215 9.46659 21.6104 9.60629 21.0641C9.97694 19.6151 10.5531 18.2254 11.3188 16.934C11.6063 16.4491 11.5784 15.8395 11.2479 15.3829L7.87577 10.7256L10.6785 7.9229L15.3356 11.2946C15.7924 11.6253 16.4018 11.6531 16.8869 11.3656C18.1783 10.5997 19.5681 10.0234 21.0172 9.65289C21.5635 9.51319 21.9746 9.06243 22.0638 8.50593L22.9722 2.83036H26.9358L27.8444 8.50593C27.9335 9.06243 28.3447 9.51319 28.8907 9.65289C30.3396 10.0234 31.7292 10.5997 33.0211 11.3656C33.506 11.6531 34.1154 11.6255 34.5722 11.2946L39.2295 7.9229L42.0322 10.7256L38.6599 15.3831C38.3293 15.8399 38.3015 16.4493 38.589 16.9343C39.3545 18.225 39.9307 19.6146 40.3017 21.0643C40.4414 21.6106 40.892 22.0215 41.4487 22.1107L47.1246 23.0193V26.9827ZM24.954 14.1652C18.9792 14.1652 14.1183 19.0261 14.1183 25.0009C14.1183 30.9757 18.9792 35.8364 24.954 35.8364C30.9288 35.8364 35.7897 30.9757 35.7897 25.0009C35.7897 19.0261 30.9288 14.1652 24.954 14.1652ZM24.954 33.0072C20.5392 33.0072 16.9477 29.4156 16.9477 25.0011C16.9477 20.5865 20.5392 16.9948 24.954 16.9948C29.3687 16.9948 32.9603 20.5865 32.9603 25.0011C32.9603 29.4156 29.3687 33.0072 24.954 33.0072Z"
                                                    fill="white" />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_3_5107">
                                                    <rect width="50" height="50" fill="white" />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                    </div>
                                    <h3 class="font-medium text-xl md:text-2xl leading-[1.4] tracking-wide mb-3">
                                        {{ __('messages.core_values') }}
                                    </h3>
                                    <p class="text-base leading-[1.4]">
                                        {{ __('messages.core_values_description') }}
                                    </p>
                                </div>
                                <div class="w-full max-w-[286px] text-center space-y-4">
                                    <div class="flex justify-center mb-4">
                                        <svg width="50" height="50" viewBox="0 0 50 50" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <g clip-path="url(#clip0_3_5112)">
                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                    d="M12.496 35.8934L11.664 36.2209C7.24966 37.9587 2.21565 35.7557 0.476041 31.3367C-1.26357 26.9177 0.917762 21.8743 5.3321 20.1365L12.3105 17.3893C17.6839 13.836 21.8002 8.01522 25.4747 3.4941L25.4755 3.49304C26.3021 2.47794 27.4976 2.02006 28.7838 2.22323C30.0699 2.42647 31.0731 3.23137 31.5499 4.44252L34.4638 11.8446C37.0489 11.3965 39.682 12.8107 40.683 15.3535C41.684 17.8963 40.7215 20.7258 38.5247 22.1602L41.4387 29.5623C41.9296 30.8094 41.7182 32.1226 40.865 33.1486C40.0132 34.173 38.7747 34.6202 37.4604 34.3688L37.4584 34.3683C31.8967 33.2948 26.056 32.2972 20.1361 33.2003L24.4032 44.0397C24.6771 44.7356 24.3364 45.5214 23.6423 45.7947L22.3407 46.3071C19.6027 47.3849 16.4838 46.0233 15.4033 43.2786L12.496 35.8934ZM35.498 14.4717L37.4905 19.5332C38.3107 18.7295 38.617 17.4811 38.1691 16.3432C37.721 15.2051 36.646 14.5007 35.498 14.4717ZM19.1211 30.622C23.6943 29.8349 28.2273 30.1258 32.6359 30.7785L24.2176 9.39423C21.4099 12.8673 18.2772 16.4736 14.5886 19.1086L19.1211 30.622ZM35.7419 31.2928C36.4867 31.4271 37.2274 31.5671 37.9634 31.7093C38.2815 31.77 38.5811 31.6613 38.7873 31.4135C38.9923 31.167 39.0427 30.8516 38.9247 30.552L29.0359 5.43218C28.9208 5.13983 28.6777 4.94681 28.3673 4.8978C28.0572 4.84889 27.7702 4.96205 27.5709 5.20678C27.1093 5.77453 26.6406 6.36131 26.1632 6.96076L35.7419 31.2928ZM12.1752 20.3534L6.32425 22.6568C3.29337 23.8499 1.7955 27.3128 2.98999 30.347C4.18441 33.3811 7.64097 34.8938 10.6719 33.7006L16.5228 31.3973L12.1752 20.3534ZM17.5149 33.9176L15.0099 34.9037L17.9172 42.2889C18.4516 43.6464 19.9943 44.3199 21.3485 43.7868L21.3932 43.7692L17.5149 33.9176ZM40.4821 8.8519C40.1852 9.53678 39.3887 9.85031 38.7044 9.55171C38.0203 9.25305 37.7059 8.45444 38.0029 7.76956L39.6597 3.94812C39.9568 3.26318 40.7533 2.94964 41.4374 3.24831C42.1215 3.54697 42.4359 4.34558 42.139 5.03046L40.4821 8.8519ZM43.8745 22.766C43.1907 22.4667 42.8771 21.6678 43.1748 20.9831C43.4724 20.2985 44.2692 19.9858 44.953 20.2851L48.7724 21.9569C49.4563 22.2563 49.7699 23.0552 49.4723 23.7398C49.1747 24.4244 48.3777 24.7372 47.6939 24.4379L43.8745 22.766ZM45.0511 15.1141C44.3574 15.3872 43.5719 15.0442 43.2981 14.3488C43.0243 13.6534 43.3653 12.8669 44.059 12.5938L47.9369 11.0672C48.6306 10.7941 49.4162 11.1371 49.69 11.8325C49.9637 12.528 49.6228 13.3144 48.9291 13.5875L45.0511 15.1141Z"
                                                    fill="white" />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_3_5112">
                                                    <rect width="50" height="50" fill="white" />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                    </div>
                                    <h3 class="font-medium text-xl md:text-2xl leading-[1.4] tracking-wide mb-3">
                                        {{ __('messages.culture') }}
                                    </h3>
                                    <p class="text-base leading-[1.4]">
                                        {{ __('messages.culture_description') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Services Section -->
                <!-- Services Section -->
                <div class="bg-img1 bg-no-repeat bg-center bg-cover mt-12 lg:mt-[70px] pb-12 lg:pb-[70px]">
                    <h2
                        class="container mx-auto text-center font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-10">
                        {{ __('messages.services_provided') }}
                    </h2>
                    <div class="services-swiper services-slider swiper-slider relative">
                        <div class="container mx-auto">
                            <div class="overflow-x-hidden -mb-[30px] sm:pb-[90px]">
                                <div class="swiper -ml-2 lg:-ml-[15px] -mr-2 lg:-mr-[15px] overflow-visible h-auto">
                                    <div class="swiper-wrapper items-start h-auto">
                                        <!-- Service Card 1 -->
                                        <div class="swiper-slide px-2 lg:px-[15px]">
                                            <div
                                                class="service-card pricing-card glass-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                                <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                    <img src="{{ asset('storage/images/sv-1.jpg') }}" alt="sv-1"
                                                        class="w-full h-full object-cover" />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                            Lorem ipsum dolor sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                    <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                        Lorem ipsum dolor sit amet
                                                    </h3>
                                                    <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                    <p class="text-white/90 leading-relaxed">
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis
                                                        fusce
                                                        mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur.
                                                        Venenatis sed habitant id sed dapibus bibendum vulputate.
                                                    </p>
                                                </div>
                                                <div class="glass-effect"></div>
                                            </div>
                                        </div>
                                        <!-- Service Card 2 -->
                                        <div class="swiper-slide px-2 lg:px-[15px]">
                                            <div
                                                class="service-card pricing-card glass-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                                <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                    <img src="{{ asset('storage/images/sv-2.jpg') }}" alt="sv-2"
                                                        class="w-full h-full object-cover" />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                            Lorem ipsum dolor sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                    <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                        Lorem ipsum dolor sit amet
                                                    </h3>
                                                    <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                    <p class="text-white/90 leading-relaxed">
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis
                                                        fusce
                                                        mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur.
                                                    </p>
                                                </div>
                                                <div class="glass-effect"></div>
                                            </div>
                                        </div>
                                        <!-- Service Card 3 -->
                                        <div class="swiper-slide px-2 lg:px-[15px]">
                                            <div
                                                class="service-card pricing-card glass-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                                <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                    <img src="{{ asset('storage/images/sv-3.jpg') }}" alt="sv-3"
                                                        class="w-full h-full object-cover" />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                            Lorem ipsum dolor sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                    <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                        Lorem ipsum dolor sit amet
                                                    </h3>
                                                    <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                    <p class="text-white/90 leading-relaxed">
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis
                                                        fusce
                                                        mauris at.
                                                    </p>
                                                </div>
                                                <div class="glass-effect"></div>
                                            </div>
                                        </div>
                                        <!-- Service Card 4 -->
                                        <div class="swiper-slide px-2 lg:px-[15px]">
                                            <div
                                                class="service-card pricing-card glass-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                                <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                    <img src="{{ asset('storage/images/sv-4.jpg') }}" alt="sv-4"
                                                        class="w-full h-full object-cover" />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                            Lorem ipsum dolor sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                    <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                        Lorem ipsum dolor sit amet
                                                    </h3>
                                                    <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                    <p class="text-white/90 leading-relaxed">
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis
                                                        fusce
                                                        mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur.
                                                        Venenatis sed habitant id sed dapibus bibendum vulputate. Diam
                                                        tortor.
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate.
                                                    </p>
                                                </div>
                                                <div class="glass-effect"></div>
                                            </div>
                                        </div>
                                        <!-- Service Card 5 (Duplicate of Card 1) -->
                                        <div class="swiper-slide px-2 lg:px-[15px]">
                                            <div
                                                class="service-card pricing-card glass-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                                <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                    <img src="{{ asset('images/sv-1.jpg') }}" alt="sv-1"
                                                        class="w-full h-full object-cover" />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                            Lorem ipsum dolor sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                    <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                        Lorem ipsum dolor sit amet
                                                    </h3>
                                                    <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                    <p class="text-white/90 leading-relaxed">
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis
                                                        fusce
                                                        mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur.
                                                        Venenatis sed habitant id sed dapibus bibendum vulputate. Diam
                                                        tortor.
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate.
                                                    </p>
                                                </div>
                                                <div class="glass-effect"></div>
                                            </div>
                                        </div>
                                        <!-- Service Card 6 (Duplicate of Card 2) -->
                                        <div class="swiper-slide px-2 lg:px-[15px]">
                                            <div
                                                class="service-card pricing-card glass-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                                <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                    <img src="{{ asset('images/sv-2.jpg') }}" alt="sv-2"
                                                        class="w-full h-full object-cover" />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                            Lorem ipsum dolor sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                    <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                        Lorem ipsum dolor sit amet
                                                    </h3>
                                                    <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                    <p class="text-white/90 leading-relaxed">
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis
                                                        fusce
                                                        mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur.
                                                        Venenatis sed habitant id sed dapibus bibendum vulputate. Diam
                                                        tortor.
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate.
                                                    </p>
                                                </div>
                                                <div class="glass-effect"></div>
                                            </div>
                                        </div>
                                        <!-- Service Card 7 (Duplicate of Card 3) -->
                                        <div class="swiper-slide px-2 lg:px-[15px]">
                                            <div
                                                class="service-card pricing-card glass-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                                <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                    <img src="{{ asset('images/sv-3.jpg') }}" alt="sv-3"
                                                        class="w-full h-full object-cover" />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                            Lorem ipsum dolor sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                    <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                        Lorem ipsum dolor sit amet
                                                    </h3>
                                                    <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                    <p class="text-white/90 leading-relaxed">
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis
                                                        fusce
                                                        mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur.
                                                        Venenatis sed habitant id sed dapibus bibendum vulputate. Diam
                                                        tortor.
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate.
                                                    </p>
                                                </div>
                                                <div class="glass-effect"></div>
                                            </div>
                                        </div>
                                        <!-- Service Card 8 (Duplicate of Card 4) -->
                                        <div class="swiper-slide px-2 lg:px-[15px]">
                                            <div
                                                class="service-card pricing-card glass-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                                <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                    <img src="{{ asset('images/sv-4.jpg') }}" alt="sv-4"
                                                        class="w-full h-full object-cover" />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                            Lorem ipsum dolor sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                    <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                        Lorem ipsum dolor sit amet
                                                    </h3>
                                                    <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                    <p class="text-white/90 leading-relaxed">
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis
                                                        fusce
                                                        mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur.
                                                        Venenatis sed habitant id sed dapibus bibendum vulputate. Diam
                                                        tortor.
                                                        Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id
                                                        sed
                                                        dapibus bibendum vulputate.
                                                    </p>
                                                    <div class="glass-effect"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            class="swiper-button-next bg-primary-base md:w-[30px] md:h-[70px] lg:w-[52px] lg:h-[104px] rounded-none right-0">
                        </div>
                        <div
                            class="swiper-button-prev bg-primary-base md:w-[30px] md:h-[70px] lg:w-[52px] lg:h-[104px] rounded-none left-0">
                        </div>
                    </div>
                </div>

                <!-- Why Choose Us Section -->
                <div class="bg-white pt-12 lg:pt-[70px] pb-12 lg:pb-[70px]">
                    <div class="container mx-auto">
                        <h2 class="text-center font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-10">
                            {{ __('messages.why_choose_us') }}
                        </h2>
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-[30px]">
                            <div class="bg-white rounded-2xl px-10 py-9 shadow-post">
                                <div
                                    class="w-full aspect-[1.37037037] overflow-hidden group-hover:scale-105 transition-transform duration-500 mb-6">
                                    <img src="{{ asset('images/image-reason-1.jpg') }}" alt="IMG-REASON"
                                        class="w-full h-full object-contain" />
                                </div>
                                <div class="text-center">
                                    <h3 class="text-xl md:text-2xl lg:text-[28px]/[1.4] font-medium mb-3">
                                        {{ __('messages.high_performance') }}
                                    </h3>
                                    <p class="text-primary-gray2 leading-[1.4]">
                                        {{ __('messages.high_performance_description') }}
                                    </p>
                                </div>
                            </div>
                            <div class="bg-white rounded-2xl px-10 py-9 shadow-post">
                                <div
                                    class="w-full aspect-[1.37037037] overflow-hidden group-hover:scale-105 transition-transform duration-500 mb-6">
                                    <img src="{{ asset('images/image-reason-2.jpg') }}" alt="IMG-REASON"
                                        class="w-full h-full object-contain" />
                                </div>
                                <div class="text-center">
                                    <h3 class="text-xl md:text-2xl lg:text-[28px]/[1.4] font-medium mb-3">
                                        {{ __('messages.always_ready') }}
                                    </h3>
                                    <p class="text-primary-gray2 leading-[1.4]">
                                        {{ __('messages.always_ready_description') }}
                                    </p>
                                </div>
                            </div>
                            <div class="bg-white rounded-2xl px-10 py-9 shadow-post">
                                <div
                                    class="w-full aspect-[1.37037037] overflow-hidden group-hover:scale-105 transition-transform duration-500 mb-6">
                                    <img src="{{ asset('images/image-reason-3.jpg') }}" alt="IMG-REASON"
                                        class="w-full h-full object-contain" />
                                </div>
                                <div class="text-center">
                                    <h3 class="text-xl md:text-2xl lg:text-[28px]/[1.4] font-medium mb-3">
                                        {{ __('messages.economical') }}
                                    </h3>
                                    <p class="text-primary-gray2 leading-[1.4]">
                                        {{ __('messages.economical_description') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Certificates Section -->
                <div class="bg-primary-grey pt-12 lg:pt-[70px] pb-12 lg:pb-[70px]">
                    <div class="container mx-auto">
                        <h2 class="text-center font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-10">
                            {{ __('messages.certificates') }}
                        </h2>
                        <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-[30px]">
                            <div class="">
                                <img src="{{ asset('images/certificate-1.jpg') }}" alt="img-certificate-1"
                                    class="w-full aspect-[1.39662447] object-cover" />
                            </div>
                            <div class="">
                                <img src="{{ asset('images/certificate-2.jpg') }}" alt="img-certificate-2"
                                    class="w-full aspect-[1.39662447] object-cover" />
                            </div>
                            <div class="">
                                <img src="{{ asset('images/certificate-3.jpg') }}" alt="img-certificate-3"
                                    class="w-full aspect-[1.39662447] object-cover" />
                            </div>
                            <div class="">
                                <img src="{{ asset('images/certificate-4.jpg') }}" alt="img-certificate-4"
                                    class="w-full aspect-[1.39662447] object-cover" />
                            </div>
                        </div>
                    </div>
                </div>

            </div>
    </main>

    <!-- Services Swiper Script -->
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            if (typeof Swiper === "undefined") {
                console.error("Swiper is not loaded!");
                return;
            }

            // Initialize Services Swiper for About page
            const servicesSwiper = new Swiper(".services-swiper .swiper", {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: false,
                autoplay: false,
                navigation: {
                    nextEl: ".services-swiper .swiper-button-next",
                    prevEl: ".services-swiper .swiper-button-prev",
                },
                breakpoints: {
                    0: {
                        slidesPerView: 2,
                        spaceBetween: 0,
                    },
                    550: {
                        slidesPerView: 2,
                        spaceBetween: 0,
                    },
                    768: {
                        slidesPerView: 2.5,
                        spaceBetween: 0,
                    },
                    1024: {
                        slidesPerView: 3,
                        spaceBetween: 0,
                    },
                    1280: {
                        slidesPerView: 4,
                        spaceBetween: 0,
                    },
                },
            });
        });
    </script>
@endsection