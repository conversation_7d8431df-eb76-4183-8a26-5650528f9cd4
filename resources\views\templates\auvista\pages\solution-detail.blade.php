@extends('templates.auvista.layouts.default')

@section('title', ($solution->title ?? __('messages.solution_detail')) . ' - Auvista')
@section('meta_description', $solution->excerpt ?? __('messages.solution_description'))

@section('content')
<!-- Main Content -->
<main id="main" id="site-main">
    <div class="page-wrapper blog-archive">

        <!-- Banner Section -->
        <div class="banner-solution relative mb-12 lg:mb-[70px]">
            <img
                src="{{ asset('images/banner-solution-detail.jpg') }}"
                alt="{{ __('messages.solution_detail') }}"
                class="w-full aspect-[1.33333333] sm:aspect-2 md:aspect-[2.92682927] object-cover"
            />
        </div>

        <div class="container mx-auto">
            <div class="wrapper-container">
                <h1 class="mb-4 text-center text-2xl lg:text-[38px]/[1.4] font-bold text-primary-base">
                    {{ $solution->title ?? __('messages.video_conferencing') }}
                </h1>
                <div class="text-center w-full max-w-[1170px] mx-auto mb-4 text-primary-gray2">
                    {{ $solution->excerpt ?? __('messages.video_conferencing_description') }}
                </div>
                <div class="text-center mb-10">
                    <button class="rounded-full text-white bg-gradient2 transition-all px-11 py-1 h-11 font-medium hover:bg-gradient5 hover:shadow-xl hover:text-primary-base hover:-translate-y-1 active:translate-y-0.5">
                        {{ __('messages.see_more') }}
                    </button>
                </div>
                <div
                    class="categories-swiper swiper-slider relative -ml-2 -mr-2"
                    data-items="5"
                    data-mobile="2.5"
                    data-tablet="3.5"
                    data-desktop="4.5"
                    data-large="5.5"
                    data-xlarge="6"
                    data-spacing="0"
                    data-loop="true"
                    data-navigation="true"
                >
                    <div class="swiper">
                        <div class="swiper-wrapper">
                            @for($i = 1; $i <= 8; $i++)
                            <div class="swiper-slide p-2 pt-2 pb-4">
                                <div class="">
                                    <a href="#" class="block overflow-hidden aspect-square rounded-xl shadow-box">
                                        <img
                                            src="{{ asset('images/demo-' . (($i-1) % 6 + 1) . '.jpg') }}"
                                            alt="demo-{{ $i }}"
                                            class="w-full h-full object-cover hover:scale-110 duration-500"
                                        />
                                    </a>
                                </div>
                            </div>
                            @endfor
                        </div>
                    </div>
                    <!-- Navigation buttons -->
                    <div class="swiper-button-next bg-primary-background3 shadow-new-shadow xl:-right-4"></div>
                    <div class="swiper-button-prev bg-primary-background3 shadow-new-shadow xl:-left-4"></div>
                </div>
            </div>
        </div>
        
        <div class="container mx-auto mt-12 lg:mt-[54px] mb-12 lg:mb-[70px]">
            <h2 class="text-center text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-10">
                {{ __('messages.featured_products') }}
            </h2>
            <div class="products list-products grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-5 xl:gap-[30px]">
                @if(isset($featuredProducts) && $featuredProducts->count() > 0)
                    @foreach($featuredProducts as $product) 
                        <!-- Product Card -->
                        <div class="product-card bg-white rounded-xl shadow-product hover:shadow-post transition-shadow duration-300 overflow-hidden group">
                            <div class="relative">
                                <!-- Product Image -->
                                <div class="">
                                    <a href="{{ route('products.show', $product->slug) }}" class="aspect-square overflow-hidden">
                                        <img
                                            src="{{ getImageUrl($product->thumbnail, 'product', 300, 300) }}"
                                            alt="{{ $product->name }}" data-img="{{ $product->thumbnail }}"
                                            class="w-full h-full object-contain p-4 group-hover:scale-105 transition-transform duration-300"
                                        />
                                    </a>
                                </div>

                                <!-- Badges -->
                                <div class="absolute top-4 left-4 flex flex-col gap-1">
                                    @if($product->discount > 0)
                                    <span class="bg-primary-badge1 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center justify-center gap-1">
                                        <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8.95525 5.86001H7.41025V2.26001C7.41025 1.42001 6.95525 1.25001 6.40025 1.88001L6.00025 2.33501L2.61525 6.18501C2.15025 6.71001 2.34525 7.14001 3.04525 7.14001H4.59025V10.74C4.59025 11.58 5.04525 11.75 5.60025 11.12L6.00025 10.665L9.38525 6.81501C9.85025 6.29001 9.65525 5.86001 8.95525 5.86001Z" fill="white"/>
                                        </svg>
                                        -{{ $product->discount }}%
                                    </span>
                                    @endif
                                    @if($product->is_featured)
                                    <span class="bg-primary-badge2 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center gap-1">
                                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6 1L7.5 4.5H11L8.25 6.75L9.75 10.25L6 8L2.25 10.25L3.75 6.75L1 4.5H4.5L6 1Z" fill="white"/>
                                        </svg>
                                        {{ __('messages.featured') }}
                                    </span>
                                    @endif
                                    @if($product->is_best_seller)
                                    <span class="bg-primary-badge3 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center gap-1">
                                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6 1.5C3.24 1.5 1 3.74 1 6.5C1 9.26 3.24 11.5 6 11.5C8.76 11.5 11 9.26 11 6.5C11 3.74 8.76 1.5 6 1.5ZM6.165 9C6.075 9.03 5.92 9.03 5.83 9C5.05 8.735 3.3 7.62 3.3 5.73C3.3 4.895 3.97 4.22 4.8 4.22C5.29 4.22 5.725 4.455 6 4.825C6.27 4.46 6.71 4.22 7.2 4.22C8.03 4.22 8.7 4.895 8.7 5.73C8.7 7.62 6.95 8.735 6.165 9Z" fill="white"/>
                                        </svg>
                                        {{ __('messages.best_seller') }}
                                    </span>
                                    @endif
                                </div>
                            </div>

                            <!-- Product Info -->
                            <div class="px-4 pb-4">
                                <div>
                                    <span class="text-sm text-primary-gray font-medium">
                                        {{ $product->category ? $product->category->name : __('messages.audio_equipment') }}
                                    </span>
                                </div>
                                <h3 class="font-medium text-lg/[1.4] line-clamp-2 cursor-pointer mb-4">
                                    <a href="{{ route('products.show', $product->slug) }}" class="text-primary-base hover:text-secondary-main transition-colors">
                                        {{ $product->name }}
                                    </a>
                                </h3>
                                <div class="flex items-center justify-between gap-1">
                                    <div class="flex flex-col">
                                        <div class="flex items-center gap-2">
                                            <span class="text-primary-price font-bold text-lg">{{ number_format($product->sale_price ?: $product->price) }}đ</span>
                                        </div>
                                        @if($product->price && $product->sale_price < $product->price)
                                        <span class="text-primary-gray font-medium line-through">{{ number_format($product->price) }}đ</span>
                                        @endif
                                    </div>
                                    <!-- Add to Cart Button -->
                                    @livewire('add-to-cart-button', [
                                        'productId' => $product->id,
                                        'showQuantity' => false,
                                    ])
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <!-- Thông báo khi không có sản phẩm -->
                    <div class="col-span-full text-center py-16">
                        <div class="text-gray-400 mb-4">
                            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <p class="text-lg text-gray-500 mb-2">{{ __('messages.no_products_yet') }}</p>
                        <p class="text-gray-400">{{ __('messages.please_come_back_later') }}</p>
                    </div>
                @endif
            </div>
            <div class="text-center mt-8">
                <button class="rounded-full text-white bg-gradient2 transition-all px-11 py-1 h-11 font-medium hover:bg-gradient5 hover:shadow-xl hover:text-primary-base hover:-translate-y-1 active:translate-y-0.5">
                    {{ __('messages.see_more') }}
                </button>
            </div>
        </div>
        
        <div class="">
            <a href="{{ route('static.page', ['slug' => 'lien-he']) }}">
                <img
                    src="{{ asset('images/banner-contact.jpg') }}"
                    alt="{{ __('messages.contact_us') }}"
                    class="w-full h-auto object-cover"
                />
            </a>
        </div>
    </div>
</main>
@endsection 