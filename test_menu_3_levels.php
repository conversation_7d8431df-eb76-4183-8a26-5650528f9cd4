<?php

/**
 * Test script để kiểm tra menu 3 cấp
 * Chạy script này để tạo dữ liệu test menu 3 cấp
 */

require_once 'vendor/autoload.php';

use App\Models\Menu;
use App\Models\MenuItem;

// Tạo menu test
$menu = Menu::create([
    'name' => 'Test Menu 3 Cấp',
    'slug' => 'test-menu-3-cap',
    'location' => 'test',
    'status' => true,
    'lang' => 'vi'
]);

// Tạo menu cấp 1
$level1_1 = MenuItem::create([
    'menu_id' => $menu->id,
    'title' => 'Sản phẩm',
    'url' => '/san-pham',
    'type' => 'custom',
    'order' => 1,
    'status' => true,
    'parent_id' => null
]);

$level1_2 = MenuItem::create([
    'menu_id' => $menu->id,
    'title' => 'Dịch vụ',
    'url' => '/dich-vu',
    'type' => 'custom',
    'order' => 2,
    'status' => true,
    'parent_id' => null
]);

// Tạo menu cấp 2 cho "Sản phẩm"
$level2_1 = MenuItem::create([
    'menu_id' => $menu->id,
    'title' => 'Điện tử',
    'url' => '/san-pham/dien-tu',
    'type' => 'custom',
    'order' => 1,
    'status' => true,
    'parent_id' => $level1_1->id
]);

$level2_2 = MenuItem::create([
    'menu_id' => $menu->id,
    'title' => 'Thời trang',
    'url' => '/san-pham/thoi-trang',
    'type' => 'custom',
    'order' => 2,
    'status' => true,
    'parent_id' => $level1_1->id
]);

// Tạo menu cấp 3 cho "Điện tử"
$level3_1 = MenuItem::create([
    'menu_id' => $menu->id,
    'title' => 'Điện thoại',
    'url' => '/san-pham/dien-tu/dien-thoai',
    'type' => 'custom',
    'order' => 1,
    'status' => true,
    'parent_id' => $level2_1->id
]);

$level3_2 = MenuItem::create([
    'menu_id' => $menu->id,
    'title' => 'Laptop',
    'url' => '/san-pham/dien-tu/laptop',
    'type' => 'custom',
    'order' => 2,
    'status' => true,
    'parent_id' => $level2_1->id
]);

// Tạo menu cấp 3 cho "Thời trang"
$level3_3 = MenuItem::create([
    'menu_id' => $menu->id,
    'title' => 'Áo nam',
    'url' => '/san-pham/thoi-trang/ao-nam',
    'type' => 'custom',
    'order' => 1,
    'status' => true,
    'parent_id' => $level2_2->id
]);

$level3_4 = MenuItem::create([
    'menu_id' => $menu->id,
    'title' => 'Áo nữ',
    'url' => '/san-pham/thoi-trang/ao-nu',
    'type' => 'custom',
    'order' => 2,
    'status' => true,
    'parent_id' => $level2_2->id
]);

echo "✅ Đã tạo thành công menu test 3 cấp!\n";
echo "Menu ID: {$menu->id}\n";
echo "Cấu trúc menu:\n";
echo "├── Sản phẩm\n";
echo "│   ├── Điện tử\n";
echo "│   │   ├── Điện thoại\n";
echo "│   │   └── Laptop\n";
echo "│   └── Thời trang\n";
echo "│       ├── Áo nam\n";
echo "│       └── Áo nữ\n";
echo "└── Dịch vụ\n";
echo "\nVào admin panel để kiểm tra: /admin/menu-items/{$menu->id}\n";
