{"__meta": {"id": "01K0B7KD750RHDS6YZYR0DK6C8", "datetime": "2025-07-17 10:42:11", "utime": **********.686272, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 8, "messages": [{"message": "[10:42:11] LOG.info: Menu cache cleared for menu: <PERSON><PERSON> (VI) {\n    \"menu_id\": 5,\n    \"menu_slug\": \"main-menu-vi\",\n    \"menu_location\": \"main-menu\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.195296, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:11] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.19911, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:11] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.285509, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:11] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.327254, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:11] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.371965, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:11] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.402132, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:11] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.425195, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:11] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.444473, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752723729.718489, "end": **********.686312, "duration": 1.9678230285644531, "duration_str": "1.97s", "measures": [{"label": "Booting", "start": 1752723729.718489, "relative_start": 0, "end": **********.493988, "relative_end": **********.493988, "duration": 0.****************, "duration_str": "775ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.494003, "relative_start": 0.****************, "end": **********.686315, "relative_end": 3.0994415283203125e-06, "duration": 1.****************, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.808281, "relative_start": 1.***************, "end": **********.818061, "relative_end": **********.818061, "duration": 0.009780168533325195, "duration_str": "9.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.admin.resources.menu-item-resource.pages.manage-menu-items", "start": **********.261782, "relative_start": 1.****************, "end": **********.261782, "relative_end": **********.261782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.26876, "relative_start": 1.****************, "end": **********.26876, "relative_end": **********.26876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.271006, "relative_start": 1.5525171756744385, "end": **********.271006, "relative_end": **********.271006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.274706, "relative_start": 1.5562169551849365, "end": **********.274706, "relative_end": **********.274706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.275823, "relative_start": 1.5573341846466064, "end": **********.275823, "relative_end": **********.275823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.276704, "relative_start": 1.5582151412963867, "end": **********.276704, "relative_end": **********.276704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.278134, "relative_start": 1.559645175933838, "end": **********.278134, "relative_end": **********.278134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.279352, "relative_start": 1.5608630180358887, "end": **********.279352, "relative_end": **********.279352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.279881, "relative_start": 1.561392068862915, "end": **********.279881, "relative_end": **********.279881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.281801, "relative_start": 1.56331205368042, "end": **********.281801, "relative_end": **********.281801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.283334, "relative_start": 1.564845085144043, "end": **********.283334, "relative_end": **********.283334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.28388, "relative_start": 1.5653910636901855, "end": **********.28388, "relative_end": **********.28388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.28416, "relative_start": 1.5656709671020508, "end": **********.28416, "relative_end": **********.28416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.284561, "relative_start": 1.5660719871520996, "end": **********.284561, "relative_end": **********.284561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.284953, "relative_start": 1.5664641857147217, "end": **********.284953, "relative_end": **********.284953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.323034, "relative_start": 1.6045451164245605, "end": **********.323034, "relative_end": **********.323034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.324037, "relative_start": 1.6055481433868408, "end": **********.324037, "relative_end": **********.324037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.325778, "relative_start": 1.6072890758514404, "end": **********.325778, "relative_end": **********.325778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.326076, "relative_start": 1.6075870990753174, "end": **********.326076, "relative_end": **********.326076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.326565, "relative_start": 1.6080760955810547, "end": **********.326565, "relative_end": **********.326565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.32684, "relative_start": 1.6083509922027588, "end": **********.32684, "relative_end": **********.32684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.364391, "relative_start": 1.645902156829834, "end": **********.364391, "relative_end": **********.364391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.365201, "relative_start": 1.646712064743042, "end": **********.365201, "relative_end": **********.365201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.367281, "relative_start": 1.648792028427124, "end": **********.367281, "relative_end": **********.367281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.367538, "relative_start": 1.6490490436553955, "end": **********.367538, "relative_end": **********.367538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.368021, "relative_start": 1.6495320796966553, "end": **********.368021, "relative_end": **********.368021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.368294, "relative_start": 1.6498050689697266, "end": **********.368294, "relative_end": **********.368294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.368525, "relative_start": 1.650036096572876, "end": **********.368525, "relative_end": **********.368525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.369751, "relative_start": 1.6512620449066162, "end": **********.369751, "relative_end": **********.369751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.369973, "relative_start": 1.6514840126037598, "end": **********.369973, "relative_end": **********.369973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.370811, "relative_start": 1.6523220539093018, "end": **********.370811, "relative_end": **********.370811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.397563, "relative_start": 1.6790740489959717, "end": **********.397563, "relative_end": **********.397563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.399626, "relative_start": 1.6811370849609375, "end": **********.399626, "relative_end": **********.399626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.400588, "relative_start": 1.6820991039276123, "end": **********.400588, "relative_end": **********.400588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.401634, "relative_start": 1.6831450462341309, "end": **********.401634, "relative_end": **********.401634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.422452, "relative_start": 1.703963041305542, "end": **********.422452, "relative_end": **********.422452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.424041, "relative_start": 1.705552101135254, "end": **********.424041, "relative_end": **********.424041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.424274, "relative_start": 1.7057850360870361, "end": **********.424274, "relative_end": **********.424274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.424758, "relative_start": 1.7062690258026123, "end": **********.424758, "relative_end": **********.424758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.465087, "relative_start": 1.7465980052947998, "end": **********.465087, "relative_end": **********.465087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.466385, "relative_start": 1.7478959560394287, "end": **********.466385, "relative_end": **********.466385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.466967, "relative_start": 1.7484781742095947, "end": **********.466967, "relative_end": **********.466967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.467645, "relative_start": 1.7491559982299805, "end": **********.467645, "relative_end": **********.467645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.46856, "relative_start": 1.7500710487365723, "end": **********.46856, "relative_end": **********.46856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.471165, "relative_start": 1.752676010131836, "end": **********.471165, "relative_end": **********.471165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.47154, "relative_start": 1.7530510425567627, "end": **********.47154, "relative_end": **********.47154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.473048, "relative_start": 1.75455904006958, "end": **********.473048, "relative_end": **********.473048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.473547, "relative_start": 1.7550580501556396, "end": **********.473547, "relative_end": **********.473547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.475261, "relative_start": 1.7567720413208008, "end": **********.475261, "relative_end": **********.475261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.475937, "relative_start": 1.7574479579925537, "end": **********.475937, "relative_end": **********.475937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.477173, "relative_start": 1.7586841583251953, "end": **********.477173, "relative_end": **********.477173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.477895, "relative_start": 1.7594060897827148, "end": **********.477895, "relative_end": **********.477895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.478284, "relative_start": 1.7597949504852295, "end": **********.478284, "relative_end": **********.478284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.478567, "relative_start": 1.760077953338623, "end": **********.478567, "relative_end": **********.478567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.479908, "relative_start": 1.7614190578460693, "end": **********.479908, "relative_end": **********.479908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.480122, "relative_start": 1.7616331577301025, "end": **********.480122, "relative_end": **********.480122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.48126, "relative_start": 1.7627711296081543, "end": **********.48126, "relative_end": **********.48126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.481939, "relative_start": 1.7634501457214355, "end": **********.481939, "relative_end": **********.481939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.484286, "relative_start": 1.7657971382141113, "end": **********.484286, "relative_end": **********.484286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.484911, "relative_start": 1.7664220333099365, "end": **********.484911, "relative_end": **********.484911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.485853, "relative_start": 1.7673640251159668, "end": **********.485853, "relative_end": **********.485853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.486197, "relative_start": 1.7677080631256104, "end": **********.486197, "relative_end": **********.486197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.48646, "relative_start": 1.7679710388183594, "end": **********.48646, "relative_end": **********.48646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.486735, "relative_start": 1.7682461738586426, "end": **********.486735, "relative_end": **********.486735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.487982, "relative_start": 1.7694931030273438, "end": **********.487982, "relative_end": **********.487982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.488189, "relative_start": 1.769700050354004, "end": **********.488189, "relative_end": **********.488189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.490289, "relative_start": 1.7718000411987305, "end": **********.490289, "relative_end": **********.490289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.491442, "relative_start": 1.7729530334472656, "end": **********.491442, "relative_end": **********.491442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.493138, "relative_start": 1.7746491432189941, "end": **********.493138, "relative_end": **********.493138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.493405, "relative_start": 1.774916172027588, "end": **********.493405, "relative_end": **********.493405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.494137, "relative_start": 1.7756481170654297, "end": **********.494137, "relative_end": **********.494137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.494462, "relative_start": 1.7759730815887451, "end": **********.494462, "relative_end": **********.494462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.494708, "relative_start": 1.776219129562378, "end": **********.494708, "relative_end": **********.494708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.494977, "relative_start": 1.7764880657196045, "end": **********.494977, "relative_end": **********.494977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.497426, "relative_start": 1.7789371013641357, "end": **********.497426, "relative_end": **********.497426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.498066, "relative_start": 1.7795770168304443, "end": **********.498066, "relative_end": **********.498066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.501127, "relative_start": 1.7826380729675293, "end": **********.501127, "relative_end": **********.501127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.501655, "relative_start": 1.7831661701202393, "end": **********.501655, "relative_end": **********.501655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.505691, "relative_start": 1.7872021198272705, "end": **********.505691, "relative_end": **********.505691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.506674, "relative_start": 1.7881851196289062, "end": **********.506674, "relative_end": **********.506674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.508158, "relative_start": 1.7896690368652344, "end": **********.508158, "relative_end": **********.508158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.508705, "relative_start": 1.7902159690856934, "end": **********.508705, "relative_end": **********.508705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.509285, "relative_start": 1.7907960414886475, "end": **********.509285, "relative_end": **********.509285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.51029, "relative_start": 1.7918009757995605, "end": **********.51029, "relative_end": **********.51029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.513958, "relative_start": 1.795469045639038, "end": **********.513958, "relative_end": **********.513958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.514566, "relative_start": 1.796077013015747, "end": **********.514566, "relative_end": **********.514566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.517554, "relative_start": 1.799065113067627, "end": **********.517554, "relative_end": **********.517554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.518345, "relative_start": 1.799856185913086, "end": **********.518345, "relative_end": **********.518345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.521458, "relative_start": 1.802968978881836, "end": **********.521458, "relative_end": **********.521458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.521938, "relative_start": 1.8034491539001465, "end": **********.521938, "relative_end": **********.521938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.523597, "relative_start": 1.8051080703735352, "end": **********.523597, "relative_end": **********.523597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.524419, "relative_start": 1.8059301376342773, "end": **********.524419, "relative_end": **********.524419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.525263, "relative_start": 1.8067741394042969, "end": **********.525263, "relative_end": **********.525263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.525931, "relative_start": 1.8074419498443604, "end": **********.525931, "relative_end": **********.525931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.529261, "relative_start": 1.810772180557251, "end": **********.529261, "relative_end": **********.529261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.53002, "relative_start": 1.8115310668945312, "end": **********.53002, "relative_end": **********.53002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.532797, "relative_start": 1.8143081665039062, "end": **********.532797, "relative_end": **********.532797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.533596, "relative_start": 1.8151071071624756, "end": **********.533596, "relative_end": **********.533596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.536652, "relative_start": 1.8181631565093994, "end": **********.536652, "relative_end": **********.536652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.537405, "relative_start": 1.8189160823822021, "end": **********.537405, "relative_end": **********.537405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.539213, "relative_start": 1.8207240104675293, "end": **********.539213, "relative_end": **********.539213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.539931, "relative_start": 1.8214421272277832, "end": **********.539931, "relative_end": **********.539931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.540328, "relative_start": 1.8218390941619873, "end": **********.540328, "relative_end": **********.540328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.54077, "relative_start": 1.8222811222076416, "end": **********.54077, "relative_end": **********.54077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.543759, "relative_start": 1.825270175933838, "end": **********.543759, "relative_end": **********.543759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.544588, "relative_start": 1.826099157333374, "end": **********.544588, "relative_end": **********.544588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.546996, "relative_start": 1.8285071849822998, "end": **********.546996, "relative_end": **********.546996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.547481, "relative_start": 1.8289921283721924, "end": **********.547481, "relative_end": **********.547481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.549665, "relative_start": 1.8311760425567627, "end": **********.549665, "relative_end": **********.549665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.550101, "relative_start": 1.8316121101379395, "end": **********.550101, "relative_end": **********.550101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.552411, "relative_start": 1.8339221477508545, "end": **********.552411, "relative_end": **********.552411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.553253, "relative_start": 1.834764003753662, "end": **********.553253, "relative_end": **********.553253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.553822, "relative_start": 1.8353331089019775, "end": **********.553822, "relative_end": **********.553822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.554299, "relative_start": 1.8358101844787598, "end": **********.554299, "relative_end": **********.554299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.557328, "relative_start": 1.838839054107666, "end": **********.557328, "relative_end": **********.557328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.557854, "relative_start": 1.839365005493164, "end": **********.557854, "relative_end": **********.557854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.561173, "relative_start": 1.842684030532837, "end": **********.561173, "relative_end": **********.561173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.561863, "relative_start": 1.8433740139007568, "end": **********.561863, "relative_end": **********.561863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.564537, "relative_start": 1.84604811668396, "end": **********.564537, "relative_end": **********.564537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.565354, "relative_start": 1.846865177154541, "end": **********.565354, "relative_end": **********.565354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.567198, "relative_start": 1.8487091064453125, "end": **********.567198, "relative_end": **********.567198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.568164, "relative_start": 1.849675178527832, "end": **********.568164, "relative_end": **********.568164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.568777, "relative_start": 1.8502881526947021, "end": **********.568777, "relative_end": **********.568777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.569258, "relative_start": 1.85076904296875, "end": **********.569258, "relative_end": **********.569258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.570989, "relative_start": 1.8524999618530273, "end": **********.570989, "relative_end": **********.570989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.57128, "relative_start": 1.8527910709381104, "end": **********.57128, "relative_end": **********.57128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.573012, "relative_start": 1.8545231819152832, "end": **********.573012, "relative_end": **********.573012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.573695, "relative_start": 1.85520601272583, "end": **********.573695, "relative_end": **********.573695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.57537, "relative_start": 1.8568811416625977, "end": **********.57537, "relative_end": **********.57537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.575827, "relative_start": 1.8573379516601562, "end": **********.575827, "relative_end": **********.575827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.576904, "relative_start": 1.858415126800537, "end": **********.576904, "relative_end": **********.576904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.577505, "relative_start": 1.8590161800384521, "end": **********.577505, "relative_end": **********.577505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.577794, "relative_start": 1.8593051433563232, "end": **********.577794, "relative_end": **********.577794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.578071, "relative_start": 1.8595821857452393, "end": **********.578071, "relative_end": **********.578071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.579922, "relative_start": 1.8614330291748047, "end": **********.579922, "relative_end": **********.579922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.580377, "relative_start": 1.8618881702423096, "end": **********.580377, "relative_end": **********.580377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.581937, "relative_start": 1.863448143005371, "end": **********.581937, "relative_end": **********.581937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.582428, "relative_start": 1.8639390468597412, "end": **********.582428, "relative_end": **********.582428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.584232, "relative_start": 1.8657431602478027, "end": **********.584232, "relative_end": **********.584232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.584666, "relative_start": 1.8661770820617676, "end": **********.584666, "relative_end": **********.584666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.585486, "relative_start": 1.8669970035552979, "end": **********.585486, "relative_end": **********.585486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.58603, "relative_start": 1.8675410747528076, "end": **********.58603, "relative_end": **********.58603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.586395, "relative_start": 1.867906093597412, "end": **********.586395, "relative_end": **********.586395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.58707, "relative_start": 1.8685810565948486, "end": **********.58707, "relative_end": **********.58707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.588784, "relative_start": 1.8702950477600098, "end": **********.588784, "relative_end": **********.588784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.589157, "relative_start": 1.8706681728363037, "end": **********.589157, "relative_end": **********.589157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.590732, "relative_start": 1.8722431659698486, "end": **********.590732, "relative_end": **********.590732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.59141, "relative_start": 1.8729209899902344, "end": **********.59141, "relative_end": **********.59141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.593006, "relative_start": 1.8745169639587402, "end": **********.593006, "relative_end": **********.593006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.593821, "relative_start": 1.8753321170806885, "end": **********.593821, "relative_end": **********.593821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.595016, "relative_start": 1.8765270709991455, "end": **********.595016, "relative_end": **********.595016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.595514, "relative_start": 1.8770251274108887, "end": **********.595514, "relative_end": **********.595514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.595784, "relative_start": 1.8772950172424316, "end": **********.595784, "relative_end": **********.595784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.596076, "relative_start": 1.877587080001831, "end": **********.596076, "relative_end": **********.596076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.597691, "relative_start": 1.879202127456665, "end": **********.597691, "relative_end": **********.597691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.598212, "relative_start": 1.879723072052002, "end": **********.598212, "relative_end": **********.598212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.59974, "relative_start": 1.8812510967254639, "end": **********.59974, "relative_end": **********.59974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.600369, "relative_start": 1.8818800449371338, "end": **********.600369, "relative_end": **********.600369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.601832, "relative_start": 1.883342981338501, "end": **********.601832, "relative_end": **********.601832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.60213, "relative_start": 1.883641004562378, "end": **********.60213, "relative_end": **********.60213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.602872, "relative_start": 1.884382963180542, "end": **********.602872, "relative_end": **********.602872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.603187, "relative_start": 1.8846981525421143, "end": **********.603187, "relative_end": **********.603187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.60343, "relative_start": 1.8849411010742188, "end": **********.60343, "relative_end": **********.60343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.603678, "relative_start": 1.8851890563964844, "end": **********.603678, "relative_end": **********.603678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.604892, "relative_start": 1.8864030838012695, "end": **********.604892, "relative_end": **********.604892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.605107, "relative_start": 1.8866181373596191, "end": **********.605107, "relative_end": **********.605107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.606256, "relative_start": 1.8877670764923096, "end": **********.606256, "relative_end": **********.606256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.606684, "relative_start": 1.8881950378417969, "end": **********.606684, "relative_end": **********.606684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.608539, "relative_start": 1.8900501728057861, "end": **********.608539, "relative_end": **********.608539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.609046, "relative_start": 1.890557050704956, "end": **********.609046, "relative_end": **********.609046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.609935, "relative_start": 1.8914461135864258, "end": **********.609935, "relative_end": **********.609935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.610341, "relative_start": 1.8918521404266357, "end": **********.610341, "relative_end": **********.610341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.610608, "relative_start": 1.8921191692352295, "end": **********.610608, "relative_end": **********.610608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.610927, "relative_start": 1.8924381732940674, "end": **********.610927, "relative_end": **********.610927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.612581, "relative_start": 1.894092082977295, "end": **********.612581, "relative_end": **********.612581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.612882, "relative_start": 1.894392967224121, "end": **********.612882, "relative_end": **********.612882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.614498, "relative_start": 1.8960089683532715, "end": **********.614498, "relative_end": **********.614498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.615181, "relative_start": 1.8966920375823975, "end": **********.615181, "relative_end": **********.615181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.616908, "relative_start": 1.8984191417694092, "end": **********.616908, "relative_end": **********.616908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.617333, "relative_start": 1.8988440036773682, "end": **********.617333, "relative_end": **********.617333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.618148, "relative_start": 1.8996591567993164, "end": **********.618148, "relative_end": **********.618148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.618505, "relative_start": 1.9000160694122314, "end": **********.618505, "relative_end": **********.618505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.618809, "relative_start": 1.900320053100586, "end": **********.618809, "relative_end": **********.618809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.619108, "relative_start": 1.9006190299987793, "end": **********.619108, "relative_end": **********.619108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.620511, "relative_start": 1.902022123336792, "end": **********.620511, "relative_end": **********.620511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.620847, "relative_start": 1.902358055114746, "end": **********.620847, "relative_end": **********.620847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.622442, "relative_start": 1.9039530754089355, "end": **********.622442, "relative_end": **********.622442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.622971, "relative_start": 1.904482126235962, "end": **********.622971, "relative_end": **********.622971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.624431, "relative_start": 1.9059419631958008, "end": **********.624431, "relative_end": **********.624431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.624822, "relative_start": 1.9063329696655273, "end": **********.624822, "relative_end": **********.624822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.625897, "relative_start": 1.9074079990386963, "end": **********.625897, "relative_end": **********.625897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.626355, "relative_start": 1.9078660011291504, "end": **********.626355, "relative_end": **********.626355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.626621, "relative_start": 1.9081320762634277, "end": **********.626621, "relative_end": **********.626621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.626877, "relative_start": 1.9083881378173828, "end": **********.626877, "relative_end": **********.626877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.628685, "relative_start": 1.91019606590271, "end": **********.628685, "relative_end": **********.628685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.629167, "relative_start": 1.9106781482696533, "end": **********.629167, "relative_end": **********.629167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.630744, "relative_start": 1.912255048751831, "end": **********.630744, "relative_end": **********.630744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.63119, "relative_start": 1.91270112991333, "end": **********.63119, "relative_end": **********.63119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.63275, "relative_start": 1.9142611026763916, "end": **********.63275, "relative_end": **********.63275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.633256, "relative_start": 1.9147670269012451, "end": **********.633256, "relative_end": **********.633256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.634047, "relative_start": 1.915558099746704, "end": **********.634047, "relative_end": **********.634047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.636386, "relative_start": 1.9178969860076904, "end": **********.636386, "relative_end": **********.636386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.637269, "relative_start": 1.9187800884246826, "end": **********.637269, "relative_end": **********.637269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.637631, "relative_start": 1.9191420078277588, "end": **********.637631, "relative_end": **********.637631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.639232, "relative_start": 1.9207429885864258, "end": **********.639232, "relative_end": **********.639232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.63981, "relative_start": 1.921321153640747, "end": **********.63981, "relative_end": **********.63981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.641746, "relative_start": 1.9232571125030518, "end": **********.641746, "relative_end": **********.641746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.642214, "relative_start": 1.9237251281738281, "end": **********.642214, "relative_end": **********.642214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.643488, "relative_start": 1.9249989986419678, "end": **********.643488, "relative_end": **********.643488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.643759, "relative_start": 1.9252700805664062, "end": **********.643759, "relative_end": **********.643759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.644185, "relative_start": 1.9256961345672607, "end": **********.644185, "relative_end": **********.644185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.645412, "relative_start": 1.9269230365753174, "end": **********.645412, "relative_end": **********.645412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.646636, "relative_start": 1.9281470775604248, "end": **********.646636, "relative_end": **********.646636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.647054, "relative_start": 1.9285650253295898, "end": **********.647054, "relative_end": **********.647054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.652693, "relative_start": 1.9342041015625, "end": **********.652693, "relative_end": **********.652693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.654215, "relative_start": 1.9357261657714844, "end": **********.654215, "relative_end": **********.654215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.654875, "relative_start": 1.9363861083984375, "end": **********.654875, "relative_end": **********.654875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.heading", "start": **********.657001, "relative_start": 1.9385120868682861, "end": **********.657001, "relative_end": **********.657001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": **********.658186, "relative_start": 1.939697027206421, "end": **********.658186, "relative_end": **********.658186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.header.index", "start": **********.661419, "relative_start": 1.942929983139038, "end": **********.661419, "relative_end": **********.661419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.actions", "start": **********.662661, "relative_start": 1.9441721439361572, "end": **********.662661, "relative_end": **********.662661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.66916, "relative_start": 1.9506709575653076, "end": **********.66916, "relative_end": **********.66916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.670323, "relative_start": 1.951833963394165, "end": **********.670323, "relative_end": **********.670323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.671988, "relative_start": 1.9534990787506104, "end": **********.671988, "relative_end": **********.671988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.672664, "relative_start": 1.9541749954223633, "end": **********.672664, "relative_end": **********.672664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.673781, "relative_start": 1.955291986465454, "end": **********.673781, "relative_end": **********.673781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.674197, "relative_start": 1.9557080268859863, "end": **********.674197, "relative_end": **********.674197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.675408, "relative_start": 1.9569189548492432, "end": **********.675408, "relative_end": **********.675408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": **********.676312, "relative_start": 1.9578230381011963, "end": **********.676312, "relative_end": **********.676312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.679543, "relative_start": 1.9610540866851807, "end": **********.681892, "relative_end": **********.681892, "duration": 0.0023488998413085938, "duration_str": "2.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 49975376, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 229, "nb_templates": 229, "templates": [{"name": "1x filament.admin.resources.menu-item-resource.pages.manage-menu-items", "param_count": null, "params": [], "start": **********.261744, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.phpfilament.admin.resources.menu-item-resource.pages.manage-menu-items", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ffilament%2Fadmin%2Fresources%2Fmenu-item-resource%2Fpages%2Fmanage-menu-items.blade.php&line=1", "ajax": false, "filename": "manage-menu-items.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.admin.resources.menu-item-resource.pages.manage-menu-items"}, {"name": "95x filament::components.icon", "param_count": null, "params": [], "start": **********.268735, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 95, "name_original": "filament::components.icon"}, {"name": "63x filament::components.button.index", "param_count": null, "params": [], "start": **********.270986, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 63, "name_original": "filament::components.button.index"}, {"name": "44x filament::components.loading-indicator", "param_count": null, "params": [], "start": **********.274675, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}, "render_count": 44, "name_original": "filament::components.loading-indicator"}, {"name": "17x filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "param_count": null, "params": [], "start": **********.465066, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/partials/wordpress-menu-item.blade.phpfilament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ffilament%2Fadmin%2Fresources%2Fmenu-item-resource%2Fpartials%2Fwordpress-menu-item.blade.php&line=1", "ajax": false, "filename": "wordpress-menu-item.blade.php", "line": "?"}, "render_count": 17, "name_original": "filament.admin.resources.menu-item-resource.partials.wordpress-menu-item"}, {"name": "1x filament::components.modal.index", "param_count": null, "params": [], "start": **********.647036, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.modal.index"}, {"name": "1x filament::components.icon-button", "param_count": null, "params": [], "start": **********.652672, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.icon-button"}, {"name": "1x filament::components.modal.heading", "param_count": null, "params": [], "start": **********.656977, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/heading.blade.phpfilament::components.modal.heading", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.modal.heading"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": **********.658163, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament-panels::components.header.index", "param_count": null, "params": [], "start": **********.661395, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/header/index.blade.phpfilament-panels::components.header.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.header.index"}, {"name": "1x filament::components.actions", "param_count": null, "params": [], "start": **********.662638, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/actions.blade.phpfilament::components.actions", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.actions"}, {"name": "2x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.669131, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": **********.676288, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}]}, "queries": {"count": 193, "nb_statements": 191, "nb_visible_statements": 193, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.11905999999999993, "accumulated_duration_str": "119ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 91 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.881426, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 3.108}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.949378, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 3.108, "width_percent": 1.352}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.9638338, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 4.46, "width_percent": 1.831}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 723}, {"index": 10, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.072601, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:723", "source": {"index": 9, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 723}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=723", "ajax": false, "filename": "ManageMenuItems.php", "line": "723"}, "connection": "auvista", "explain": null, "start_percent": 6.291, "width_percent": 0}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.07301, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 6.291, "width_percent": 1.277}, {"sql": "select * from `menus` where `menus`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 185}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 774}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 24, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.076399, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:185", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=185", "ajax": false, "filename": "ManageMenuItems.php", "line": "185"}, "connection": "auvista", "explain": null, "start_percent": 7.568, "width_percent": 0.815}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.078246, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 8.382, "width_percent": 0.386}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.080143, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 8.769, "width_percent": 1.016}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 24, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}], "start": **********.082751, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 9.785, "width_percent": 0.748}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 24, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}], "start": **********.084886, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 10.533, "width_percent": 0.731}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 24, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}], "start": **********.087575, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 11.263, "width_percent": 0.731}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 24, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}], "start": **********.089547, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 11.994, "width_percent": 0.479}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.090962, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 12.473, "width_percent": 0.479}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.092379, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 12.951, "width_percent": 0.571}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.094415, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 13.523, "width_percent": 0.882}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 799}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.09663, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 14.405, "width_percent": 0.428}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.0979092, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 14.833, "width_percent": 0.378}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.099155, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 15.211, "width_percent": 0.361}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.100866, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 15.572, "width_percent": 2.142}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.104629, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 17.714, "width_percent": 0.748}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.106677, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 18.461, "width_percent": 0.966}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 726}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.109019, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:773", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 773}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=773", "ajax": false, "filename": "ManageMenuItems.php", "line": "773"}, "connection": "auvista", "explain": null, "start_percent": 19.427, "width_percent": 0.815}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 728}, {"index": 10, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.117083, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:728", "source": {"index": 9, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 728}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=728", "ajax": false, "filename": "ManageMenuItems.php", "line": "728"}, "connection": "auvista", "explain": null, "start_percent": 20.242, "width_percent": 0}, {"sql": "select * from `menus` where `menus`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 731}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 914}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.1173081, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:731", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 731}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=731", "ajax": false, "filename": "ManageMenuItems.php", "line": "731"}, "connection": "auvista", "explain": null, "start_percent": 20.242, "width_percent": 0.521}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.199481, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 20.763, "width_percent": 0.999}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.2018712, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 21.762, "width_percent": 0.378}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.203094, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 22.14, "width_percent": 0.353}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.20481, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 22.493, "width_percent": 1.033}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.207109, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 23.526, "width_percent": 0.512}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.208527, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 24.038, "width_percent": 0.311}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.209614, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 24.349, "width_percent": 0.294}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.210971, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 24.643, "width_percent": 1.277}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.213604, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 25.92, "width_percent": 1.067}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.215691, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 26.986, "width_percent": 0.286}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.216783, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 27.272, "width_percent": 0.269}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.218273, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 27.541, "width_percent": 1.075}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.2209198, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 28.616, "width_percent": 0.512}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.222291, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 29.128, "width_percent": 0.302}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.223365, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 29.431, "width_percent": 0.277}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.224655, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 29.708, "width_percent": 0.638}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.22664, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 30.346, "width_percent": 0.823}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.228595, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 31.169, "width_percent": 0.386}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2354279, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 31.556, "width_percent": 0.949}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.237417, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 32.505, "width_percent": 0.311}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.240754, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 32.815, "width_percent": 0.907}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.243064, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 33.722, "width_percent": 0.319}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.2452312, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 34.042, "width_percent": 0.689}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.250849, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 34.73, "width_percent": 0.596}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.285699, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 35.327, "width_percent": 0.773}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.288345, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 36.099, "width_percent": 1.041}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.290822, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 37.141, "width_percent": 0.445}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.292007, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 37.586, "width_percent": 0.336}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.2930079, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 37.922, "width_percent": 0.344}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.2943718, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 38.266, "width_percent": 0.924}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.2966669, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 39.19, "width_percent": 0.756}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.298362, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 39.946, "width_percent": 0.286}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.299318, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 40.232, "width_percent": 0.294}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.300287, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 40.526, "width_percent": 0.294}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.301789, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 40.82, "width_percent": 0.865}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.303798, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 41.685, "width_percent": 0.798}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.305492, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 42.483, "width_percent": 0.294}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.306464, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 42.777, "width_percent": 0.269}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3074179, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 43.046, "width_percent": 0.336}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.309023, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 43.381, "width_percent": 0.924}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.311145, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 44.305, "width_percent": 0.428}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3124719, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 44.734, "width_percent": 0.311}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.313437, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 45.045, "width_percent": 0.244}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.314304, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 45.288, "width_percent": 0.235}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.3161712, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 45.523, "width_percent": 0.84}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.318569, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 46.363, "width_percent": 0.319}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.319796, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 46.682, "width_percent": 0.252}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.320985, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 46.934, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.327438, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 47.161, "width_percent": 0.714}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.329638, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 47.875, "width_percent": 0.638}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3316522, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 48.513, "width_percent": 0.706}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3332329, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 49.219, "width_percent": 0.302}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.334237, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 49.521, "width_percent": 0.286}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3352268, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 49.807, "width_percent": 0.361}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.336986, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 50.168, "width_percent": 1.016}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.339398, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 51.184, "width_percent": 0.336}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.340471, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 51.52, "width_percent": 0.286}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.341411, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 51.806, "width_percent": 0.286}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.34248, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 52.091, "width_percent": 0.512}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.344227, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 52.604, "width_percent": 0.815}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.346293, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 53.418, "width_percent": 0.311}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3473449, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 53.729, "width_percent": 0.361}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.348404, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 54.09, "width_percent": 0.58}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.350199, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 54.67, "width_percent": 0.941}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.352323, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 55.611, "width_percent": 0.706}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.353918, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 56.316, "width_percent": 0.244}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.354809, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 56.56, "width_percent": 0.252}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3556721, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 56.812, "width_percent": 0.235}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.357487, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 57.047, "width_percent": 0.924}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.359773, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 57.971, "width_percent": 0.504}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.36133, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 58.475, "width_percent": 0.277}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.362548, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 58.752, "width_percent": 0.252}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.372281, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 59.004, "width_percent": 1.151}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.374847, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 60.155, "width_percent": 0.328}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.375913, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 60.482, "width_percent": 0.286}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3769069, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 60.768, "width_percent": 0.344}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.378534, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 61.112, "width_percent": 0.815}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.380425, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 61.927, "width_percent": 0.605}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.381939, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 62.531, "width_percent": 0.302}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3825321, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 62.834, "width_percent": 0.277}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.383064, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 63.111, "width_percent": 0.563}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3839571, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 63.674, "width_percent": 0.269}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.384721, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 63.943, "width_percent": 1.268}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.386831, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 65.211, "width_percent": 0.84}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388317, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 66.051, "width_percent": 0.26}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388865, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 66.311, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3893292, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 66.529, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.389787, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 66.756, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390244, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 66.975, "width_percent": 0.302}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390804, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 67.277, "width_percent": 0.286}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391526, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 67.563, "width_percent": 0.79}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.393132, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 68.352, "width_percent": 0.689}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.394959, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 69.041, "width_percent": 0.302}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.395685, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 69.343, "width_percent": 0.218}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3961601, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 69.562, "width_percent": 0.227}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.396757, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 69.788, "width_percent": 0.21}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4023108, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 69.998, "width_percent": 0.63}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.403356, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 70.628, "width_percent": 0.235}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4038422, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 70.863, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4043128, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 71.09, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.404781, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 71.309, "width_percent": 0.252}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4054298, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 71.561, "width_percent": 0.781}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.406952, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.342, "width_percent": 0.899}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40841, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.24, "width_percent": 0.563}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.409317, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.803, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4097862, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.03, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4102588, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.257, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.410718, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.475, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411172, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.693, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411642, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.912, "width_percent": 0.294}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.412361, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.206, "width_percent": 0.916}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4139829, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.121, "width_percent": 0.512}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4152071, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.634, "width_percent": 0.277}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4157982, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.911, "width_percent": 0.244}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4163022, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.154, "width_percent": 0.235}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4167962, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.39, "width_percent": 0.227}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.417672, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.616, "width_percent": 0.277}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.418327, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.893, "width_percent": 0.227}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4190261, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 78.12, "width_percent": 0.848}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.420612, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 78.969, "width_percent": 0.672}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.42539, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 79.641, "width_percent": 0.521}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426635, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.161, "width_percent": 0.974}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.428222, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.136, "width_percent": 0.344}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.429097, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.48, "width_percent": 0.26}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.429624, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.74, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.430094, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.967, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43057, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 82.194, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.431031, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 82.412, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4314868, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 82.631, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.431947, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 82.849, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.432402, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 83.067, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.433065, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 83.286, "width_percent": 0.916}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4346302, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.201, "width_percent": 0.563}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.435856, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.764, "width_percent": 0.252}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.436429, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.016, "width_percent": 0.311}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4370098, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.327, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.437475, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.545, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4379199, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.763, "width_percent": 0.218}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43837, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.982, "width_percent": 0.244}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.438866, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.225, "width_percent": 0.286}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.44003, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.511, "width_percent": 1.016}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.441855, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 87.527, "width_percent": 0.512}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4429889, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.04, "width_percent": 0.302}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.443708, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.342, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.444621, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.56, "width_percent": 0.403}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4453912, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.964, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.44587, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.19, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.446416, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.409, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.447002, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.627, "width_percent": 0.932}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.448554, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.559, "width_percent": 0.487}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4494321, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.047, "width_percent": 0.395}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.450164, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.441, "width_percent": 0.235}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4506571, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.676, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.45112, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.903, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.451574, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.122, "width_percent": 0.26}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.452109, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.382, "width_percent": 0.235}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.452584, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.617, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4530602, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.844, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.453784, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.088, "width_percent": 0.999}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.455435, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.087, "width_percent": 0.748}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.456902, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.835, "width_percent": 0.344}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.457557, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.179, "width_percent": 0.605}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458481, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.784, "width_percent": 0.235}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4589741, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.019, "width_percent": 0.227}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.459839, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.246, "width_percent": 0.269}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.460704, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.514, "width_percent": 1.764}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4632359, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.278, "width_percent": 0.462}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.464263, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.74, "width_percent": 0.252}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.634764, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.992, "width_percent": 1.008}]}, "models": {"data": {"App\\Models\\MenuItem": {"value": 255, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Category": {"value": 210, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\StaticPage": {"value": 196, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FStaticPage.php&line=1", "ajax": false, "filename": "StaticPage.php", "line": "?"}}, "App\\Models\\Brand": {"value": 126, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Post": {"value": 113, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 49, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Menu": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 992, "is_counter": true}, "livewire": {"data": {"app.filament.resources.menu-item-resource.pages.manage-menu-items #0j0CpraYz3207cNNcrxL": "array:4 [\n  \"data\" => array:51 [\n    \"isMenuReordering\" => false\n    \"newMenuItemData\" => null\n    \"selectedTab\" => \"custom\"\n    \"customUrl\" => null\n    \"customTitle\" => null\n    \"selectedPages\" => []\n    \"selectedPosts\" => []\n    \"selectedCategories\" => []\n    \"selectedBrands\" => []\n    \"selectedMenuItems\" => []\n    \"editingMenuItem\" => []\n    \"refreshCounter\" => 2\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:2 [\n      \"type\" => array:1 [\n        \"value\" => null\n      ]\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:3 [\n      \"url\" => true\n      \"type\" => true\n      \"status\" => true\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.menu-item-resource.pages.manage-menu-items\"\n  \"component\" => \"App\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems\"\n  \"id\" => \"0j0CpraYz3207cNNcrxL\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "1.97s", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-594176923 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-594176923\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2016254356 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E5i5PJh9l9VwKtI1wpqPfTuNVse14pMEKeu2ITQy</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"2137 characters\">{&quot;data&quot;:{&quot;isMenuReordering&quot;:false,&quot;newMenuItemData&quot;:null,&quot;selectedTab&quot;:&quot;custom&quot;,&quot;customUrl&quot;:null,&quot;customTitle&quot;:null,&quot;selectedPages&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedPosts&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedCategories&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedBrands&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedMenuItems&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editingMenuItem&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;refreshCounter&quot;:0,&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;type&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;status&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areFormStateUpdateHooksDisabledForTesting&quot;:false,&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[{&quot;url&quot;:true,&quot;type&quot;:true,&quot;status&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;0j0CpraYz3207cNNcrxL&quot;,&quot;name&quot;:&quot;app.filament.resources.menu-item-resource.pages.manage-menu-items&quot;,&quot;path&quot;:&quot;admin\\/menu-items\\/5&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;vi&quot;},&quot;checksum&quot;:&quot;147fb733131abda7cbb19107a6e601297193afa42334188c4d0646751f981d81&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__dispatch</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">reorderMenuItem</span>\"\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>17</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>18</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>children</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>\n                      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n                      \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>18</span>\n                      \"<span class=sf-dump-key>children</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n                          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n                          \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>19</span>\n                        </samp>]\n                        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>21</span>\n                          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n                          \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>19</span>\n                        </samp>]\n                        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>22</span>\n                          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n                          \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>19</span>\n                        </samp>]\n                        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>23</span>\n                          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n                          \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>19</span>\n                        </samp>]\n                      </samp>]\n                    </samp>]\n                    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>24</span>\n                      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n                      \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>18</span>\n                    </samp>]\n                    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>25</span>\n                      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n                      \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>18</span>\n                    </samp>]\n                    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>26</span>\n                      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n                      \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>18</span>\n                    </samp>]\n                    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>27</span>\n                      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n                      \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>18</span>\n                    </samp>]\n                  </samp>]\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>28</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>29</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>30</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>31</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>5</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>32</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>6</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>7</span>\n                  \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n              </samp>]\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016254356\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-971176468 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3222</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">https://auvista.test/admin/menu-items/5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InZxSzc0Y0VVelVTeWlzYTlURndTQkE9PSIsInZhbHVlIjoiOS85aWtpb2Q5T3M1MER3MUxiNWtkZWx1bkdKNW9kSW5BWUZOOTVtMExJbHhna2x1MDg2L0grL0pKajVJK3hiWEVDNkQ0MEFmWVEzSTlUMktxSHFaSDNyMXI1YzZmYkhUank0V0paN2xPcVJveFlralpMZWRXU2hNNko4UnB6T1UiLCJtYWMiOiI4ZDFmYzFmMmMxOGQ2NTliYzE1MzJjYjc2NDk5NmZjMDNlYjJjMjZlZDU4Mzg1NzJiNTQ5OTc1NGI5ZWZiYWEwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik1zNEtPTnY3Z1NzS0h1aUxnbTFDU1E9PSIsInZhbHVlIjoiZHpPdTF0cmdacXhXdHc2KzlkblA0YVNsaUxDWUNIK0NOMVFEVUk4eXRpaW9JTEtXbVZDWXVWRGRRS09HOTU4Q1ArVlRUV0FmUlErSmRHOE1Fd1VNL1hzVFZHdEhDcWphUlg4czFsZmJUUXBsZXo2MWtudlE0VUg5dm9temYvSUkiLCJtYWMiOiIzOTM5NzM0MmNhYWMzMjgzNDVmYWM1MDU5MzQ1MDIxN2M3OTliNWI1ZWNhNTBhZTJkYTg0ZjgwNTUzN2M5YjQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-971176468\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1581609358 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E5i5PJh9l9VwKtI1wpqPfTuNVse14pMEKeu2ITQy</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T5AEVWUx3GFIfIinUvq16vAwcsjmwqju4fDR0Pfs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1581609358\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1407927745 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 03:42:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1407927745\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-629462182 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E5i5PJh9l9VwKtI1wpqPfTuNVse14pMEKeu2ITQy</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">https://auvista.test/admin/menu-items/5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$2uJGkMnwk7Lj203GgLThU.EI1Z7VNTd1oXu5zgFDqiWVi2sEnDYEy</span>\"\n  \"<span class=sf-dump-key>current_menu_id</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>filament</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>notifications</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f68c789-e163-4812-b5a6-6113e1751e58</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Th&#7913; t&#7921; v&#224; c&#7845;u tr&#250;c menu &#273;a c&#7845;p &#273;&#227; &#273;&#432;&#7907;c l&#432;u</span>\"\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Menu &#273;&#227; &#273;&#432;&#7907;c c&#7853;p nh&#7853;t</span>\"\n        \"<span class=sf-dump-key>view</span>\" => \"<span class=sf-dump-str title=\"36 characters\">filament-notifications::notification</span>\"\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f68c789-ea7b-4b4b-acff-6e65f91ca5ea</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Th&#7913; t&#7921; menu &#273;&#227; &#273;&#432;&#7907;c c&#7853;p nh&#7853;t.</span>\"\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">C&#7853;p nh&#7853;t th&#224;nh c&#244;ng</span>\"\n        \"<span class=sf-dump-key>view</span>\" => \"<span class=sf-dump-str title=\"36 characters\">filament-notifications::notification</span>\"\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-629462182\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}