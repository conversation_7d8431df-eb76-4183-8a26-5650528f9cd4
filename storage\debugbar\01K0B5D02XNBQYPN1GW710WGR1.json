{"__meta": {"id": "01K0B5D02XNBQYPN1GW710WGR1", "datetime": "2025-07-17 10:03:44", "utime": **********.478007, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 7, "messages": [{"message": "[10:03:43] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 4\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.965905, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:44] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 4\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.050507, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:44] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 4\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.10702, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:44] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 4\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.168259, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:44] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 4\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.216693, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:44] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 4\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.244804, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:44] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 4\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.272331, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.17784, "end": **********.478062, "duration": 1.3002219200134277, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": **********.17784, "relative_start": 0, "end": **********.587284, "relative_end": **********.587284, "duration": 0.*****************, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.587304, "relative_start": 0.*****************, "end": **********.478065, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "891ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.819773, "relative_start": 0.****************, "end": **********.823318, "relative_end": **********.823318, "duration": 0.003545045852661133, "duration_str": "3.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.admin.resources.menu-item-resource.pages.manage-menu-items", "start": **********.031087, "relative_start": 0.****************, "end": **********.031087, "relative_end": **********.031087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.035876, "relative_start": 0.****************, "end": **********.035876, "relative_end": **********.035876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.037202, "relative_start": 0.8593618869781494, "end": **********.037202, "relative_end": **********.037202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.040611, "relative_start": 0.8627710342407227, "end": **********.040611, "relative_end": **********.040611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.041035, "relative_start": 0.8631949424743652, "end": **********.041035, "relative_end": **********.041035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.041625, "relative_start": 0.8637850284576416, "end": **********.041625, "relative_end": **********.041625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.043368, "relative_start": 0.8655281066894531, "end": **********.043368, "relative_end": **********.043368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.045328, "relative_start": 0.867487907409668, "end": **********.045328, "relative_end": **********.045328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.046278, "relative_start": 0.8684380054473877, "end": **********.046278, "relative_end": **********.046278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.047874, "relative_start": 0.8700339794158936, "end": **********.047874, "relative_end": **********.047874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.048369, "relative_start": 0.8705289363861084, "end": **********.048369, "relative_end": **********.048369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.048832, "relative_start": 0.8709919452667236, "end": **********.048832, "relative_end": **********.048832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.049115, "relative_start": 0.8712749481201172, "end": **********.049115, "relative_end": **********.049115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.049507, "relative_start": 0.8716669082641602, "end": **********.049507, "relative_end": **********.049507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.049883, "relative_start": 0.8720428943634033, "end": **********.049883, "relative_end": **********.049883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.102783, "relative_start": 0.9249429702758789, "end": **********.102783, "relative_end": **********.102783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.103353, "relative_start": 0.9255130290985107, "end": **********.103353, "relative_end": **********.103353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.104936, "relative_start": 0.927095890045166, "end": **********.104936, "relative_end": **********.104936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.105316, "relative_start": 0.9274759292602539, "end": **********.105316, "relative_end": **********.105316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.105775, "relative_start": 0.9279351234436035, "end": **********.105775, "relative_end": **********.105775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.106108, "relative_start": 0.9282679557800293, "end": **********.106108, "relative_end": **********.106108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.160655, "relative_start": 0.9828150272369385, "end": **********.160655, "relative_end": **********.160655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.161137, "relative_start": 0.9832971096038818, "end": **********.161137, "relative_end": **********.161137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.163121, "relative_start": 0.9852809906005859, "end": **********.163121, "relative_end": **********.163121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.163788, "relative_start": 0.9859480857849121, "end": **********.163788, "relative_end": **********.163788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.164682, "relative_start": 0.9868419170379639, "end": **********.164682, "relative_end": **********.164682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.16508, "relative_start": 0.9872400760650635, "end": **********.16508, "relative_end": **********.16508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.165455, "relative_start": 0.9876151084899902, "end": **********.165455, "relative_end": **********.165455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.16693, "relative_start": 0.9890899658203125, "end": **********.16693, "relative_end": **********.16693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.167254, "relative_start": 0.9894139766693115, "end": **********.167254, "relative_end": **********.167254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.167732, "relative_start": 0.9898920059204102, "end": **********.167732, "relative_end": **********.167732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.2141, "relative_start": 1.0362598896026611, "end": **********.2141, "relative_end": **********.2141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.215546, "relative_start": 1.037705898284912, "end": **********.215546, "relative_end": **********.215546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.215811, "relative_start": 1.037971019744873, "end": **********.215811, "relative_end": **********.215811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.216258, "relative_start": 1.0384180545806885, "end": **********.216258, "relative_end": **********.216258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.242228, "relative_start": 1.0643880367279053, "end": **********.242228, "relative_end": **********.242228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.243676, "relative_start": 1.065835952758789, "end": **********.243676, "relative_end": **********.243676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.243945, "relative_start": 1.0661048889160156, "end": **********.243945, "relative_end": **********.243945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.244352, "relative_start": 1.066512107849121, "end": **********.244352, "relative_end": **********.244352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.296858, "relative_start": 1.1190180778503418, "end": **********.296858, "relative_end": **********.296858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.29794, "relative_start": 1.1201000213623047, "end": **********.29794, "relative_end": **********.29794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.298574, "relative_start": 1.1207339763641357, "end": **********.298574, "relative_end": **********.298574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.298995, "relative_start": 1.121155023574829, "end": **********.298995, "relative_end": **********.298995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.299333, "relative_start": 1.1214931011199951, "end": **********.299333, "relative_end": **********.299333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.300777, "relative_start": 1.1229369640350342, "end": **********.300777, "relative_end": **********.300777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.301095, "relative_start": 1.1232550144195557, "end": **********.301095, "relative_end": **********.301095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.302478, "relative_start": 1.1246380805969238, "end": **********.302478, "relative_end": **********.302478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.302933, "relative_start": 1.1250929832458496, "end": **********.302933, "relative_end": **********.302933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.304453, "relative_start": 1.126612901687622, "end": **********.304453, "relative_end": **********.304453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.304733, "relative_start": 1.1268930435180664, "end": **********.304733, "relative_end": **********.304733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.305612, "relative_start": 1.1277720928192139, "end": **********.305612, "relative_end": **********.305612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.306008, "relative_start": 1.1281681060791016, "end": **********.306008, "relative_end": **********.306008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.306319, "relative_start": 1.12847900390625, "end": **********.306319, "relative_end": **********.306319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.306686, "relative_start": 1.1288459300994873, "end": **********.306686, "relative_end": **********.306686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.308147, "relative_start": 1.1303069591522217, "end": **********.308147, "relative_end": **********.308147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.308432, "relative_start": 1.1305921077728271, "end": **********.308432, "relative_end": **********.308432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.309902, "relative_start": 1.1320619583129883, "end": **********.309902, "relative_end": **********.309902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.31023, "relative_start": 1.132390022277832, "end": **********.31023, "relative_end": **********.31023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.311542, "relative_start": 1.133702039718628, "end": **********.311542, "relative_end": **********.311542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.31182, "relative_start": 1.1339800357818604, "end": **********.31182, "relative_end": **********.31182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.312617, "relative_start": 1.1347770690917969, "end": **********.312617, "relative_end": **********.312617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.312975, "relative_start": 1.1351349353790283, "end": **********.312975, "relative_end": **********.312975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.313251, "relative_start": 1.135411024093628, "end": **********.313251, "relative_end": **********.313251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.313539, "relative_start": 1.1356990337371826, "end": **********.313539, "relative_end": **********.313539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.314979, "relative_start": 1.137139081954956, "end": **********.314979, "relative_end": **********.314979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.315263, "relative_start": 1.137423038482666, "end": **********.315263, "relative_end": **********.315263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.316551, "relative_start": 1.1387109756469727, "end": **********.316551, "relative_end": **********.316551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.316886, "relative_start": 1.1390459537506104, "end": **********.316886, "relative_end": **********.316886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.31814, "relative_start": 1.1403000354766846, "end": **********.31814, "relative_end": **********.31814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.318399, "relative_start": 1.1405589580535889, "end": **********.318399, "relative_end": **********.318399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.319116, "relative_start": 1.1412761211395264, "end": **********.319116, "relative_end": **********.319116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.319457, "relative_start": 1.1416170597076416, "end": **********.319457, "relative_end": **********.319457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.319725, "relative_start": 1.1418850421905518, "end": **********.319725, "relative_end": **********.319725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.320002, "relative_start": 1.1421620845794678, "end": **********.320002, "relative_end": **********.320002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.321394, "relative_start": 1.1435539722442627, "end": **********.321394, "relative_end": **********.321394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.321721, "relative_start": 1.14388108253479, "end": **********.321721, "relative_end": **********.321721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.323109, "relative_start": 1.1452689170837402, "end": **********.323109, "relative_end": **********.323109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.323437, "relative_start": 1.145596981048584, "end": **********.323437, "relative_end": **********.323437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.324985, "relative_start": 1.1471450328826904, "end": **********.324985, "relative_end": **********.324985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.325447, "relative_start": 1.1476070880889893, "end": **********.325447, "relative_end": **********.325447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.326346, "relative_start": 1.1485059261322021, "end": **********.326346, "relative_end": **********.326346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.326722, "relative_start": 1.1488819122314453, "end": **********.326722, "relative_end": **********.326722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.326981, "relative_start": 1.1491410732269287, "end": **********.326981, "relative_end": **********.326981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.327237, "relative_start": 1.1493968963623047, "end": **********.327237, "relative_end": **********.327237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.328679, "relative_start": 1.15083909034729, "end": **********.328679, "relative_end": **********.328679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.328945, "relative_start": 1.1511049270629883, "end": **********.328945, "relative_end": **********.328945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.33026, "relative_start": 1.1524200439453125, "end": **********.33026, "relative_end": **********.33026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.330652, "relative_start": 1.1528120040893555, "end": **********.330652, "relative_end": **********.330652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.331972, "relative_start": 1.1541318893432617, "end": **********.331972, "relative_end": **********.331972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.332251, "relative_start": 1.1544110774993896, "end": **********.332251, "relative_end": **********.332251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.333053, "relative_start": 1.1552131175994873, "end": **********.333053, "relative_end": **********.333053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.333426, "relative_start": 1.1555860042572021, "end": **********.333426, "relative_end": **********.333426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.333718, "relative_start": 1.1558780670166016, "end": **********.333718, "relative_end": **********.333718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.334024, "relative_start": 1.1561839580535889, "end": **********.334024, "relative_end": **********.334024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.335487, "relative_start": 1.157646894454956, "end": **********.335487, "relative_end": **********.335487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.33576, "relative_start": 1.1579201221466064, "end": **********.33576, "relative_end": **********.33576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.337142, "relative_start": 1.159301996231079, "end": **********.337142, "relative_end": **********.337142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.337469, "relative_start": 1.1596291065216064, "end": **********.337469, "relative_end": **********.337469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.338828, "relative_start": 1.1609880924224854, "end": **********.338828, "relative_end": **********.338828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.339108, "relative_start": 1.1612679958343506, "end": **********.339108, "relative_end": **********.339108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.340004, "relative_start": 1.1621639728546143, "end": **********.340004, "relative_end": **********.340004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.340651, "relative_start": 1.162811040878296, "end": **********.340651, "relative_end": **********.340651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.341096, "relative_start": 1.1632559299468994, "end": **********.341096, "relative_end": **********.341096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.341529, "relative_start": 1.1636888980865479, "end": **********.341529, "relative_end": **********.341529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.343094, "relative_start": 1.1652541160583496, "end": **********.343094, "relative_end": **********.343094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.343407, "relative_start": 1.1655669212341309, "end": **********.343407, "relative_end": **********.343407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.344902, "relative_start": 1.1670620441436768, "end": **********.344902, "relative_end": **********.344902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.345345, "relative_start": 1.1675050258636475, "end": **********.345345, "relative_end": **********.345345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.346615, "relative_start": 1.1687750816345215, "end": **********.346615, "relative_end": **********.346615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.346908, "relative_start": 1.1690680980682373, "end": **********.346908, "relative_end": **********.346908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.347648, "relative_start": 1.1698079109191895, "end": **********.347648, "relative_end": **********.347648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.347959, "relative_start": 1.170119047164917, "end": **********.347959, "relative_end": **********.347959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.348203, "relative_start": 1.170362949371338, "end": **********.348203, "relative_end": **********.348203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.348458, "relative_start": 1.1706180572509766, "end": **********.348458, "relative_end": **********.348458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.349839, "relative_start": 1.1719989776611328, "end": **********.349839, "relative_end": **********.349839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.350123, "relative_start": 1.1722829341888428, "end": **********.350123, "relative_end": **********.350123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.351312, "relative_start": 1.1734719276428223, "end": **********.351312, "relative_end": **********.351312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.351601, "relative_start": 1.1737608909606934, "end": **********.351601, "relative_end": **********.351601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.35275, "relative_start": 1.174910068511963, "end": **********.35275, "relative_end": **********.35275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.352989, "relative_start": 1.1751489639282227, "end": **********.352989, "relative_end": **********.352989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.353704, "relative_start": 1.1758639812469482, "end": **********.353704, "relative_end": **********.353704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.354013, "relative_start": 1.1761729717254639, "end": **********.354013, "relative_end": **********.354013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.354255, "relative_start": 1.176414966583252, "end": **********.354255, "relative_end": **********.354255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.354507, "relative_start": 1.1766669750213623, "end": **********.354507, "relative_end": **********.354507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.355699, "relative_start": 1.1778590679168701, "end": **********.355699, "relative_end": **********.355699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.35622, "relative_start": 1.178380012512207, "end": **********.35622, "relative_end": **********.35622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.358291, "relative_start": 1.1804509162902832, "end": **********.358291, "relative_end": **********.358291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.358648, "relative_start": 1.1808080673217773, "end": **********.358648, "relative_end": **********.358648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.359863, "relative_start": 1.182023048400879, "end": **********.359863, "relative_end": **********.359863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.360126, "relative_start": 1.182286024093628, "end": **********.360126, "relative_end": **********.360126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.360861, "relative_start": 1.183021068572998, "end": **********.360861, "relative_end": **********.360861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.361229, "relative_start": 1.1833889484405518, "end": **********.361229, "relative_end": **********.361229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.3615, "relative_start": 1.1836600303649902, "end": **********.3615, "relative_end": **********.3615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.361774, "relative_start": 1.183933973312378, "end": **********.361774, "relative_end": **********.361774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.363069, "relative_start": 1.1852290630340576, "end": **********.363069, "relative_end": **********.363069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.363412, "relative_start": 1.1855719089508057, "end": **********.363412, "relative_end": **********.363412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.364764, "relative_start": 1.1869239807128906, "end": **********.364764, "relative_end": **********.364764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.365104, "relative_start": 1.1872639656066895, "end": **********.365104, "relative_end": **********.365104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.366293, "relative_start": 1.188452959060669, "end": **********.366293, "relative_end": **********.366293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.366567, "relative_start": 1.1887269020080566, "end": **********.366567, "relative_end": **********.366567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.367313, "relative_start": 1.1894729137420654, "end": **********.367313, "relative_end": **********.367313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.367637, "relative_start": 1.1897969245910645, "end": **********.367637, "relative_end": **********.367637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.36789, "relative_start": 1.1900498867034912, "end": **********.36789, "relative_end": **********.36789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.368154, "relative_start": 1.1903140544891357, "end": **********.368154, "relative_end": **********.368154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.369432, "relative_start": 1.1915919780731201, "end": **********.369432, "relative_end": **********.369432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.369668, "relative_start": 1.1918280124664307, "end": **********.369668, "relative_end": **********.369668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.371049, "relative_start": 1.193208932876587, "end": **********.371049, "relative_end": **********.371049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.371455, "relative_start": 1.1936149597167969, "end": **********.371455, "relative_end": **********.371455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.373019, "relative_start": 1.1951789855957031, "end": **********.373019, "relative_end": **********.373019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.373569, "relative_start": 1.1957290172576904, "end": **********.373569, "relative_end": **********.373569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.374488, "relative_start": 1.196648120880127, "end": **********.374488, "relative_end": **********.374488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.374868, "relative_start": 1.1970279216766357, "end": **********.374868, "relative_end": **********.374868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.37512, "relative_start": 1.197279930114746, "end": **********.37512, "relative_end": **********.37512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.375423, "relative_start": 1.1975829601287842, "end": **********.375423, "relative_end": **********.375423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.376857, "relative_start": 1.19901704788208, "end": **********.376857, "relative_end": **********.376857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.377253, "relative_start": 1.1994130611419678, "end": **********.377253, "relative_end": **********.377253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.378684, "relative_start": 1.2008440494537354, "end": **********.378684, "relative_end": **********.378684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.379183, "relative_start": 1.201343059539795, "end": **********.379183, "relative_end": **********.379183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.380614, "relative_start": 1.2027740478515625, "end": **********.380614, "relative_end": **********.380614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.380926, "relative_start": 1.2030858993530273, "end": **********.380926, "relative_end": **********.380926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.381701, "relative_start": 1.2038609981536865, "end": **********.381701, "relative_end": **********.381701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.38219, "relative_start": 1.2043499946594238, "end": **********.38219, "relative_end": **********.38219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.382472, "relative_start": 1.204632043838501, "end": **********.382472, "relative_end": **********.382472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.382745, "relative_start": 1.2049050331115723, "end": **********.382745, "relative_end": **********.382745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.38406, "relative_start": 1.2062199115753174, "end": **********.38406, "relative_end": **********.38406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.384423, "relative_start": 1.206583023071289, "end": **********.384423, "relative_end": **********.384423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.385881, "relative_start": 1.2080409526824951, "end": **********.385881, "relative_end": **********.385881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.386298, "relative_start": 1.2084579467773438, "end": **********.386298, "relative_end": **********.386298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.38778, "relative_start": 1.209939956665039, "end": **********.38778, "relative_end": **********.38778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.388177, "relative_start": 1.2103369235992432, "end": **********.388177, "relative_end": **********.388177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.389016, "relative_start": 1.2111759185791016, "end": **********.389016, "relative_end": **********.389016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.389368, "relative_start": 1.2115280628204346, "end": **********.389368, "relative_end": **********.389368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.389619, "relative_start": 1.2117791175842285, "end": **********.389619, "relative_end": **********.389619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.389879, "relative_start": 1.2120389938354492, "end": **********.389879, "relative_end": **********.389879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.391255, "relative_start": 1.2134149074554443, "end": **********.391255, "relative_end": **********.391255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.39161, "relative_start": 1.2137699127197266, "end": **********.39161, "relative_end": **********.39161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.392874, "relative_start": 1.215034008026123, "end": **********.392874, "relative_end": **********.392874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.393165, "relative_start": 1.215325117111206, "end": **********.393165, "relative_end": **********.393165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.39434, "relative_start": 1.2165000438690186, "end": **********.39434, "relative_end": **********.39434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.394612, "relative_start": 1.2167720794677734, "end": **********.394612, "relative_end": **********.394612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.395363, "relative_start": 1.2175230979919434, "end": **********.395363, "relative_end": **********.395363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.395691, "relative_start": 1.217850923538208, "end": **********.395691, "relative_end": **********.395691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.395937, "relative_start": 1.2180969715118408, "end": **********.395937, "relative_end": **********.395937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.396187, "relative_start": 1.2183470726013184, "end": **********.396187, "relative_end": **********.396187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.397433, "relative_start": 1.2195930480957031, "end": **********.397433, "relative_end": **********.397433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.397673, "relative_start": 1.2198328971862793, "end": **********.397673, "relative_end": **********.397673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.399018, "relative_start": 1.2211780548095703, "end": **********.399018, "relative_end": **********.399018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.399357, "relative_start": 1.2215170860290527, "end": **********.399357, "relative_end": **********.399357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.400571, "relative_start": 1.222731113433838, "end": **********.400571, "relative_end": **********.400571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.400837, "relative_start": 1.2229969501495361, "end": **********.400837, "relative_end": **********.400837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.401573, "relative_start": 1.2237329483032227, "end": **********.401573, "relative_end": **********.401573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.403107, "relative_start": 1.225266933441162, "end": **********.403107, "relative_end": **********.403107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.403673, "relative_start": 1.2258329391479492, "end": **********.403673, "relative_end": **********.403673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.403982, "relative_start": 1.2261419296264648, "end": **********.403982, "relative_end": **********.403982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.405445, "relative_start": 1.2276051044464111, "end": **********.405445, "relative_end": **********.405445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.405742, "relative_start": 1.2279019355773926, "end": **********.405742, "relative_end": **********.405742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.407015, "relative_start": 1.229175090789795, "end": **********.407015, "relative_end": **********.407015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.407353, "relative_start": 1.2295129299163818, "end": **********.407353, "relative_end": **********.407353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.40859, "relative_start": 1.2307500839233398, "end": **********.40859, "relative_end": **********.40859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.408869, "relative_start": 1.2310290336608887, "end": **********.408869, "relative_end": **********.408869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.409633, "relative_start": 1.23179292678833, "end": **********.409633, "relative_end": **********.409633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.409973, "relative_start": 1.232132911682129, "end": **********.409973, "relative_end": **********.409973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.410236, "relative_start": 1.232395887374878, "end": **********.410236, "relative_end": **********.410236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.410509, "relative_start": 1.2326691150665283, "end": **********.410509, "relative_end": **********.410509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.411849, "relative_start": 1.234009027481079, "end": **********.411849, "relative_end": **********.411849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.412171, "relative_start": 1.2343308925628662, "end": **********.412171, "relative_end": **********.412171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.413479, "relative_start": 1.2356390953063965, "end": **********.413479, "relative_end": **********.413479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.413866, "relative_start": 1.2360260486602783, "end": **********.413866, "relative_end": **********.413866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.415205, "relative_start": 1.2373650074005127, "end": **********.415205, "relative_end": **********.415205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.415477, "relative_start": 1.2376370429992676, "end": **********.415477, "relative_end": **********.415477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.415889, "relative_start": 1.238049030303955, "end": **********.415889, "relative_end": **********.415889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.417147, "relative_start": 1.239306926727295, "end": **********.417147, "relative_end": **********.417147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.418471, "relative_start": 1.240631103515625, "end": **********.418471, "relative_end": **********.418471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.419597, "relative_start": 1.2417569160461426, "end": **********.419597, "relative_end": **********.419597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.422375, "relative_start": 1.244534969329834, "end": **********.422375, "relative_end": **********.422375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.423485, "relative_start": 1.2456450462341309, "end": **********.423485, "relative_end": **********.423485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.423959, "relative_start": 1.2461190223693848, "end": **********.423959, "relative_end": **********.423959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.heading", "start": **********.424412, "relative_start": 1.2465720176696777, "end": **********.424412, "relative_end": **********.424412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": **********.424888, "relative_start": 1.2470479011535645, "end": **********.424888, "relative_end": **********.424888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.header.index", "start": **********.426596, "relative_start": 1.248755931854248, "end": **********.426596, "relative_end": **********.426596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.actions", "start": **********.42713, "relative_start": 1.2492899894714355, "end": **********.42713, "relative_end": **********.42713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.429702, "relative_start": 1.2518620491027832, "end": **********.429702, "relative_end": **********.429702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.430241, "relative_start": 1.2524011135101318, "end": **********.430241, "relative_end": **********.430241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.431651, "relative_start": 1.2538111209869385, "end": **********.431651, "relative_end": **********.431651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.432194, "relative_start": 1.2543540000915527, "end": **********.432194, "relative_end": **********.432194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.433748, "relative_start": 1.2559080123901367, "end": **********.433748, "relative_end": **********.433748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.434195, "relative_start": 1.2563550472259521, "end": **********.434195, "relative_end": **********.434195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.435957, "relative_start": 1.2581169605255127, "end": **********.435957, "relative_end": **********.435957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": **********.436795, "relative_start": 1.2589550018310547, "end": **********.436795, "relative_end": **********.436795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.449188, "relative_start": 1.271347999572754, "end": **********.449188, "relative_end": **********.449188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.460845, "relative_start": 1.2830049991607666, "end": **********.460845, "relative_end": **********.460845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.462105, "relative_start": 1.2842650413513184, "end": **********.462105, "relative_end": **********.462105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.464354, "relative_start": 1.2865140438079834, "end": **********.464354, "relative_end": **********.464354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.465855, "relative_start": 1.2880148887634277, "end": **********.465855, "relative_end": **********.465855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.46745, "relative_start": 1.2896099090576172, "end": **********.46745, "relative_end": **********.46745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.469755, "relative_start": 1.291914939880371, "end": **********.469755, "relative_end": **********.469755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.470951, "relative_start": 1.2931110858917236, "end": **********.470951, "relative_end": **********.470951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.472017, "relative_start": 1.2941770553588867, "end": **********.472017, "relative_end": **********.472017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.474075, "relative_start": 1.2962350845336914, "end": **********.476038, "relative_end": **********.476038, "duration": 0.001962900161743164, "duration_str": "1.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 51058152, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 238, "nb_templates": 238, "templates": [{"name": "1x filament.admin.resources.menu-item-resource.pages.manage-menu-items", "param_count": null, "params": [], "start": **********.031053, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.phpfilament.admin.resources.menu-item-resource.pages.manage-menu-items", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ffilament%2Fadmin%2Fresources%2Fmenu-item-resource%2Fpages%2Fmanage-menu-items.blade.php&line=1", "ajax": false, "filename": "manage-menu-items.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.admin.resources.menu-item-resource.pages.manage-menu-items"}, {"name": "101x filament::components.icon", "param_count": null, "params": [], "start": **********.035847, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 101, "name_original": "filament::components.icon"}, {"name": "63x filament::components.button.index", "param_count": null, "params": [], "start": **********.037173, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 63, "name_original": "filament::components.button.index"}, {"name": "44x filament::components.loading-indicator", "param_count": null, "params": [], "start": **********.040588, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}, "render_count": 44, "name_original": "filament::components.loading-indicator"}, {"name": "17x filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "param_count": null, "params": [], "start": **********.296836, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/partials/wordpress-menu-item.blade.phpfilament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ffilament%2Fadmin%2Fresources%2Fmenu-item-resource%2Fpartials%2Fwordpress-menu-item.blade.php&line=1", "ajax": false, "filename": "wordpress-menu-item.blade.php", "line": "?"}, "render_count": 17, "name_original": "filament.admin.resources.menu-item-resource.partials.wordpress-menu-item"}, {"name": "1x filament::components.modal.index", "param_count": null, "params": [], "start": **********.419545, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.modal.index"}, {"name": "4x filament::components.icon-button", "param_count": null, "params": [], "start": **********.422356, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 4, "name_original": "filament::components.icon-button"}, {"name": "1x filament::components.modal.heading", "param_count": null, "params": [], "start": **********.424396, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/heading.blade.phpfilament::components.modal.heading", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.modal.heading"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": **********.424867, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament-panels::components.header.index", "param_count": null, "params": [], "start": **********.426576, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/header/index.blade.phpfilament-panels::components.header.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.header.index"}, {"name": "1x filament::components.actions", "param_count": null, "params": [], "start": **********.427113, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/actions.blade.phpfilament::components.actions", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.actions"}, {"name": "2x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.429683, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": **********.436773, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}]}, "queries": {"count": 173, "nb_statements": 173, "nb_visible_statements": 173, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.14894999999999997, "accumulated_duration_str": "149ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 73 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.853856, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 2.558}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.892629, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 2.558, "width_percent": 0.846}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.9031892, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 3.404, "width_percent": 1.954}, {"sql": "select * from `menus` where `menus`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 185}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 337}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 359}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.959927, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:185", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=185", "ajax": false, "filename": "ManageMenuItems.php", "line": "185"}, "connection": "auvista", "explain": null, "start_percent": 5.358, "width_percent": 0.947}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.966572, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 6.304, "width_percent": 1.893}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.970958, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 8.197, "width_percent": 0.846}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.973718, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 9.043, "width_percent": 0.866}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.97703, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 9.909, "width_percent": 1.443}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.980331, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 11.353, "width_percent": 0.631}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.982505, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 11.984, "width_percent": 0.953}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.984884, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 12.937, "width_percent": 0.564}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.986613, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 13.501, "width_percent": 0.436}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.988281, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 13.938, "width_percent": 0.739}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.990783, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 14.676, "width_percent": 0.604}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.992589, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 15.28, "width_percent": 0.342}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.993894, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 15.623, "width_percent": 0.302}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.995147, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 15.925, "width_percent": 0.598}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.997214, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 16.522, "width_percent": 0.544}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.998853, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 17.066, "width_percent": 0.222}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.999942, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 17.288, "width_percent": 0.215}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.001019, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 17.503, "width_percent": 0.235}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.005077, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 17.737, "width_percent": 0.51}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.006584, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 18.248, "width_percent": 0.269}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.0077012, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 18.516, "width_percent": 0.215}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.009972, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 18.731, "width_percent": 0.678}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.012318, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 19.409, "width_percent": 0.51}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.014992, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 19.919, "width_percent": 0.98}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.022123, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 20.9, "width_percent": 1.067}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.050962, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 21.967, "width_percent": 1.195}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.0545409, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 23.162, "width_percent": 0.624}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.056705, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 23.787, "width_percent": 0.678}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.059202, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 24.465, "width_percent": 1.027}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.061913, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 25.492, "width_percent": 0.631}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.063921, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 26.123, "width_percent": 0.618}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.066171, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 26.741, "width_percent": 0.947}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.06863, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 27.687, "width_percent": 0.517}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.0703468, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 28.204, "width_percent": 0.504}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.072323, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 28.708, "width_percent": 0.779}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.0746732, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 29.486, "width_percent": 0.443}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.076299, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 29.93, "width_percent": 0.41}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.0778491, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 30.339, "width_percent": 0.416}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.07969, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 30.755, "width_percent": 0.618}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.081707, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 31.373, "width_percent": 0.477}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.083332, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 31.85, "width_percent": 0.436}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.0850031, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 32.286, "width_percent": 0.43}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.086926, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 32.716, "width_percent": 1.034}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.089315, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 33.75, "width_percent": 0.49}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.090951, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 34.24, "width_percent": 0.517}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.0933568, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 34.757, "width_percent": 0.987}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.096182, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 35.744, "width_percent": 0.443}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.097926, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 36.187, "width_percent": 0.403}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.099972, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 36.589, "width_percent": 0.692}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.107397, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 37.281, "width_percent": 1.047}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.110188, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 38.328, "width_percent": 0.443}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.111728, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 38.771, "width_percent": 0.383}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.1133358, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 39.154, "width_percent": 0.859}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.11582, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 40.013, "width_percent": 0.584}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.11767, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 40.598, "width_percent": 0.457}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.119335, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 41.054, "width_percent": 0.403}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.121539, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 41.457, "width_percent": 1.242}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.124423, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 42.699, "width_percent": 0.443}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.1262019, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 43.142, "width_percent": 0.517}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.128302, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 43.659, "width_percent": 0.913}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.1307042, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 44.572, "width_percent": 0.557}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.132505, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 45.129, "width_percent": 0.356}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.1340508, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 45.485, "width_percent": 0.879}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.136665, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 46.365, "width_percent": 0.826}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.138953, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 47.19, "width_percent": 0.933}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.141769, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 48.124, "width_percent": 1.396}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.1449661, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 49.52, "width_percent": 0.584}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.1467571, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 50.104, "width_percent": 0.537}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.148745, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 50.641, "width_percent": 0.906}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.1517131, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 51.547, "width_percent": 0.571}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.153792, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 52.118, "width_percent": 0.53}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.156138, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 52.649, "width_percent": 0.839}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.1586912, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 53.488, "width_percent": 0.443}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.1685581, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 53.931, "width_percent": 1.047}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.171668, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 54.978, "width_percent": 0.819}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.1739259, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 55.797, "width_percent": 0.517}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.175701, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 56.314, "width_percent": 0.98}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.178411, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 57.294, "width_percent": 0.598}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.1802409, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 57.892, "width_percent": 0.598}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.1821961, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 58.489, "width_percent": 0.772}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.1846871, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 59.261, "width_percent": 0.557}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.186342, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 59.819, "width_percent": 0.383}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.187617, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 60.201, "width_percent": 0.295}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.188718, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 60.497, "width_percent": 0.208}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.1899529, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 60.705, "width_percent": 0.638}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.192049, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 61.343, "width_percent": 0.477}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.193547, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 61.819, "width_percent": 0.356}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.194796, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 62.175, "width_percent": 0.329}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.195964, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 62.504, "width_percent": 0.242}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.197505, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 62.746, "width_percent": 0.658}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.1994321, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 63.404, "width_percent": 0.43}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.200818, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 63.834, "width_percent": 1.202}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.203467, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 65.035, "width_percent": 0.725}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.2061949, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 65.76, "width_percent": 0.53}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.208061, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 66.291, "width_percent": 0.423}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.209622, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 66.714, "width_percent": 0.396}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.211533, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 67.11, "width_percent": 0.725}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.216955, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 67.835, "width_percent": 0.873}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.218934, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 68.708, "width_percent": 0.584}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.220141, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 69.292, "width_percent": 0.248}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.220817, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 69.54, "width_percent": 0.316}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.221538, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 69.856, "width_percent": 0.188}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.222059, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 70.044, "width_percent": 0.443}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.223032, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 70.487, "width_percent": 0.282}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.223816, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 70.769, "width_percent": 0.51}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.225225, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 71.279, "width_percent": 0.859}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.226907, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.138, "width_percent": 0.43}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2278912, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.568, "width_percent": 0.295}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.228571, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.863, "width_percent": 0.262}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.22918, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.125, "width_percent": 0.336}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.229934, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.461, "width_percent": 0.336}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.230684, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.797, "width_percent": 0.269}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.231472, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.065, "width_percent": 0.483}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2327151, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.549, "width_percent": 0.906}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.234499, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.455, "width_percent": 0.389}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.235359, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.844, "width_percent": 0.282}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.235975, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.126, "width_percent": 0.248}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.23701, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.375, "width_percent": 0.376}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.237982, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.751, "width_percent": 0.497}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.23923, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.247, "width_percent": 0.839}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2410898, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 78.087, "width_percent": 0.396}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.245135, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 78.483, "width_percent": 1.014}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.247262, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 79.496, "width_percent": 0.618}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.248512, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.114, "width_percent": 0.215}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.249077, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.329, "width_percent": 0.316}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.249803, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.645, "width_percent": 0.356}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2505999, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81, "width_percent": 0.269}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.251225, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.269, "width_percent": 0.248}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.251891, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.517, "width_percent": 0.611}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2533422, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 82.128, "width_percent": 0.886}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2550628, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 83.014, "width_percent": 0.53}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.256113, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 83.545, "width_percent": 0.329}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.256842, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 83.874, "width_percent": 0.262}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.257489, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.136, "width_percent": 0.336}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.258254, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.471, "width_percent": 0.322}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2591462, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.794, "width_percent": 0.537}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.260501, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.331, "width_percent": 0.853}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.262249, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.183, "width_percent": 0.396}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.263123, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.579, "width_percent": 0.309}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.263849, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.888, "width_percent": 0.49}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2648752, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 87.378, "width_percent": 0.322}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.266231, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 87.701, "width_percent": 0.537}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2675638, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.238, "width_percent": 0.624}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2689528, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.862, "width_percent": 0.41}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.270358, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.272, "width_percent": 0.812}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.272607, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.084, "width_percent": 1.047}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.274746, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.131, "width_percent": 0.577}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2760088, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.709, "width_percent": 0.289}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.276863, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.997, "width_percent": 0.302}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.277816, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.299, "width_percent": 0.913}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.279714, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.212, "width_percent": 0.698}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2812989, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.911, "width_percent": 0.718}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.282892, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.629, "width_percent": 0.43}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.283966, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.059, "width_percent": 0.262}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.28465, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.321, "width_percent": 0.215}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2852662, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.535, "width_percent": 0.201}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.285852, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.737, "width_percent": 0.222}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2865272, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.958, "width_percent": 0.302}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2874331, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.26, "width_percent": 0.45}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2884822, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.71, "width_percent": 0.309}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.289256, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.019, "width_percent": 0.215}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.289856, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.234, "width_percent": 0.255}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.290522, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.489, "width_percent": 0.222}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2911632, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.711, "width_percent": 0.295}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.291898, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.006, "width_percent": 0.269}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.293219, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.275, "width_percent": 0.463}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2944658, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.738, "width_percent": 0.41}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.295416, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 99.147, "width_percent": 0.228}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2961571, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 99.376, "width_percent": 0.215}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402022, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 99.59, "width_percent": 0.41}]}, "models": {"data": {"App\\Models\\MenuItem": {"value": 238, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Category": {"value": 210, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\StaticPage": {"value": 196, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FStaticPage.php&line=1", "ajax": false, "filename": "StaticPage.php", "line": "?"}}, "App\\Models\\Brand": {"value": 126, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Post": {"value": 113, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 49, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Menu": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}}, "count": 974, "is_counter": true}, "livewire": {"data": {"app.filament.resources.menu-item-resource.pages.manage-menu-items #Gr9dYh2zsodYZ4GvRlG9": "array:4 [\n  \"data\" => array:51 [\n    \"isMenuReordering\" => false\n    \"newMenuItemData\" => null\n    \"selectedTab\" => \"custom\"\n    \"customUrl\" => null\n    \"customTitle\" => null\n    \"selectedPages\" => []\n    \"selectedPosts\" => []\n    \"selectedCategories\" => []\n    \"selectedBrands\" => []\n    \"selectedMenuItems\" => []\n    \"editingMenuItem\" => []\n    \"refreshCounter\" => 4\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:2 [\n      \"type\" => array:1 [\n        \"value\" => null\n      ]\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:3 [\n      \"url\" => true\n      \"type\" => true\n      \"status\" => true\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.menu-item-resource.pages.manage-menu-items\"\n  \"component\" => \"App\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems\"\n  \"id\" => \"Gr9dYh2zsodYZ4GvRlG9\"\n]", "filament.livewire.notifications #qKmeBwiUiLh4iSKBzrho": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2660\n      #items: array:3 [\n        \"9f68b9b3-2df7-4bed-a821-dd6ddc6333a0\" => Filament\\Notifications\\Notification {#2656\n          #evaluationIdentifier: ? string\n          #view: \"filament-notifications::notification\"\n          #defaultView: null\n          #viewData: array:1 [\n            0 => []\n          ]\n          #viewIdentifier: \"notification\"\n          #safeViews: []\n          #isInline: false\n          #actions: []\n          #body: \"Thứ tự menu đã được cập nhật.\"\n          #date: null\n          #duration: 6000\n          #icon: \"heroicon-o-check-circle\"\n          #iconPosition: null\n          #iconSize: null\n          #iconColor: \"success\"\n          #id: \"9f68b9b3-2df7-4bed-a821-dd6ddc6333a0\"\n          #status: \"success\"\n          #title: \"Cập nhật thành công\"\n          #color: null\n          #defaultColor: null\n        }\n        \"9f68b9c5-229b-4fcf-bd14-15ecc3c0825f\" => Filament\\Notifications\\Notification {#3136\n          #evaluationIdentifier: ? string\n          #view: \"filament-notifications::notification\"\n          #defaultView: null\n          #viewData: array:1 [\n            0 => []\n          ]\n          #viewIdentifier: \"notification\"\n          #safeViews: []\n          #isInline: false\n          #actions: []\n          #body: \"Thứ tự và cấu trúc menu đa cấp đã được lưu\"\n          #date: null\n          #duration: 6000\n          #icon: \"heroicon-o-check-circle\"\n          #iconPosition: null\n          #iconSize: null\n          #iconColor: \"success\"\n          #id: \"9f68b9c5-229b-4fcf-bd14-15ecc3c0825f\"\n          #status: \"success\"\n          #title: \"Menu đã được cập nhật\"\n          #color: null\n          #defaultColor: null\n        }\n        \"9f68b9c5-26bf-4eee-8790-6bdd2b3692be\" => Filament\\Notifications\\Notification {#3139\n          #evaluationIdentifier: ? string\n          #view: \"filament-notifications::notification\"\n          #defaultView: null\n          #viewData: array:1 [\n            0 => []\n          ]\n          #viewIdentifier: \"notification\"\n          #safeViews: []\n          #isInline: false\n          #actions: []\n          #body: \"Thứ tự menu đã được cập nhật.\"\n          #date: null\n          #duration: 6000\n          #icon: \"heroicon-o-check-circle\"\n          #iconPosition: null\n          #iconSize: null\n          #iconColor: \"success\"\n          #id: \"9f68b9c5-26bf-4eee-8790-6bdd2b3692be\"\n          #status: \"success\"\n          #title: \"Cập nhật thành công\"\n          #color: null\n          #defaultColor: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"qKmeBwiUiLh4iSKBzrho\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "1.31s", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-816879863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-816879863\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2135224741 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E5i5PJh9l9VwKtI1wpqPfTuNVse14pMEKeu2ITQy</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"2137 characters\">{&quot;data&quot;:{&quot;isMenuReordering&quot;:false,&quot;newMenuItemData&quot;:null,&quot;selectedTab&quot;:&quot;custom&quot;,&quot;customUrl&quot;:null,&quot;customTitle&quot;:null,&quot;selectedPages&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedPosts&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedCategories&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedBrands&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedMenuItems&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editingMenuItem&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;refreshCounter&quot;:4,&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;type&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;status&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areFormStateUpdateHooksDisabledForTesting&quot;:false,&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[{&quot;url&quot;:true,&quot;type&quot;:true,&quot;status&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;Gr9dYh2zsodYZ4GvRlG9&quot;,&quot;name&quot;:&quot;app.filament.resources.menu-item-resource.pages.manage-menu-items&quot;,&quot;path&quot;:&quot;admin\\/menu-items\\/5&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;vi&quot;},&quot;checksum&quot;:&quot;f6538e997abb474e5d2f26eecd8c9f8c800c9c7cf84bbe5e1289296c9e4b56ed&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"832 characters\">{&quot;data&quot;:{&quot;isFilamentNotificationsComponent&quot;:true,&quot;notifications&quot;:[{&quot;9f68b9b3-2df7-4bed-a821-dd6ddc6333a0&quot;:[{&quot;id&quot;:&quot;9f68b9b3-2df7-4bed-a821-dd6ddc6333a0&quot;,&quot;actions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;body&quot;:&quot;Th\\u1ee9 t\\u1ef1 menu \\u0111\\u00e3 \\u0111\\u01b0\\u1ee3c c\\u1eadp nh\\u1eadt.&quot;,&quot;color&quot;:null,&quot;duration&quot;:6000,&quot;icon&quot;:&quot;heroicon-o-check-circle&quot;,&quot;iconColor&quot;:&quot;success&quot;,&quot;status&quot;:&quot;success&quot;,&quot;title&quot;:&quot;C\\u1eadp nh\\u1eadt th\\u00e0nh c\\u00f4ng&quot;,&quot;view&quot;:&quot;filament-notifications::notification&quot;,&quot;viewData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;Filament\\\\Notifications\\\\Collection&quot;,&quot;s&quot;:&quot;wrbl&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;qKmeBwiUiLh4iSKBzrho&quot;,&quot;name&quot;:&quot;filament.livewire.notifications&quot;,&quot;path&quot;:&quot;admin\\/menu-items\\/5&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;vi&quot;},&quot;checksum&quot;:&quot;50142a3a8b6a20d91af2137292115c4486b320be0420dfe8136fc85ad305bbec&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__dispatch</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">notificationsSent</span>\"\n            <span class=sf-dump-index>1</span> => []\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__dispatch</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">notificationsSent</span>\"\n            <span class=sf-dump-index>1</span> => []\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135224741\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1111697795 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3635</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">https://auvista.test/admin/menu-items/5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImIyQ2F2NW1IcHBKeHprM3FKMGt3aFE9PSIsInZhbHVlIjoiYi8xb0o5MW5pZzZ4V3pCMzlrQU1nUEl2WE05YWdscUx5ZXh6aXFmYWNQdVlwQkdNZ1M3QnhPUVlkMTZjdnZOckQxUmtoaUFOZWNEZzNTWUpoanAzNjluSUx3T2RzQlMvT3l2Q3BMRjU2L0w2YWNoU2UrZVNxR2RnemdhZ0hIbWYiLCJtYWMiOiIwYjhjOTcwMTM5OGUzZmUzMTQ0YWExNzQ4YTdhYzFhNmI0MDU1NWY4ZDBjOGNhYzk1ZDg1MWM5NjM3NTQ4YzNlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imw1c2tHWWRjbGpEQStlenlaRmE4U2c9PSIsInZhbHVlIjoibHY0cDAxRXR6V0U1Sit5aElBYkdXK2xMZXBJMDdpQmlSUTNpRGFWTVM4RVJabk0xdVJmRUk4RVpucDZ0M2FPcXJGOHhoSDlETEFNMmpUWGdlY0dubVVzbkhoL3N0cVNvTlVQUUNoUzlvZlZBOCtDMXhRQXRMa2s1UlN4cUI1aVoiLCJtYWMiOiIwMGZkYTJmZjQ4OTI4ZDQ5MTgzMzUzYjJiN2U5OWUwZjc3OTcxNjc0NTc1ZTVlMTJhNTg0NWFjZDA2OTU1N2YxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1111697795\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1081515442 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E5i5PJh9l9VwKtI1wpqPfTuNVse14pMEKeu2ITQy</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T5AEVWUx3GFIfIinUvq16vAwcsjmwqju4fDR0Pfs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081515442\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1736920547 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 03:03:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1736920547\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-256257007 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E5i5PJh9l9VwKtI1wpqPfTuNVse14pMEKeu2ITQy</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">https://auvista.test/san-pham</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$2uJGkMnwk7Lj203GgLThU.EI1Z7VNTd1oXu5zgFDqiWVi2sEnDYEy</span>\"\n  \"<span class=sf-dump-key>current_menu_id</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-256257007\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}