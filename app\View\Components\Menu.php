<?php

namespace App\View\Components;

use App\Models\Menu as MenuModel;
use App\Models\MenuItem;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use Illuminate\Support\Facades\Cache;

class Menu extends Component
{
    public $menu;
    public $items;

    /**
     * Create a new component instance.
     */
    public function __construct(string $name)
    {
        // Sử dụng cache để cải thiện hiệu suất
        $cacheKey = 'menu_' . $name;
        
        // Cache menu trong 60 phút, trừ khi được xóa thủ công khi cập nhật menu
        $this->menu = Cache::remember($cacheKey . '_model', 3600, function() use ($name) {
            return MenuModel::where('slug', $name)
                ->where('status', true)
                ->first();
        });
        
        if ($this->menu) {
            // Cache menu items (hỗ trợ đa cấp không giới hạn)
            $this->items = Cache::remember($cacheKey . '_items', 3600, function() {
                // Lấy menu items cấp cao nhất (không có parent)
                return $this->menu->items()
                    ->whereNull('parent_id')
                    ->where('status', true)
                    ->orderBy('order')
                    ->with($this->getNestedChildrenRelation())
                    ->get();
            });
        } else {
            $this->items = collect();
        }
    }

    /**
     * Tạo nested relation cho children đa cấp
     */
    private function getNestedChildrenRelation($depth = 5)
    {
        $relation = ['children' => function($query) use ($depth) {
            $query->where('status', true)->orderBy('order');

            if ($depth > 1) {
                $query->with($this->getNestedChildrenRelation($depth - 1));
            }
        }];

        return $relation;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.menu');
    }
}
