<?php

namespace App\Filament\Resources\MenuItemResource\Pages;

use App\Filament\Resources\MenuItemResource;
use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\StaticPage;
use App\Models\Post;
use App\Models\Category;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Support\Exceptions\Halt;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\On;
use Illuminate\Database\Eloquent\Model;
use Livewire\Attributes\Computed;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Toggle;
use Illuminate\Support\Facades\Cache;

class ManageMenuItems extends ManageRecords
{
    protected static string $resource = MenuItemResource::class;
    
    // Flag để kiểm soát trạng thái kéo thả của menu
    public bool $isMenuReordering = false;
    
    // Dữ liệu tạm cho form thêm menu item
    public ?array $newMenuItemData = null;
    
    // Tab đang chọn (custom, page, post, category)
    public string $selectedTab = 'custom';
    
    // Các dữ liệu cho form thêm menu item
    public ?string $customUrl = null;
    public ?string $customTitle = null;
    public array $selectedPages = [];
    public array $selectedPosts = [];
    public array $selectedCategories = [];
    public array $selectedBrands = [];
    public array $selectedMenuItems = [];

    // Dữ liệu cho form chỉnh sửa menu item
    public array $editingMenuItem = [];

    // Cache menu object
    private ?Menu $menu = null;

    // Force refresh counter
    public int $refreshCounter = 0;

    // Lưu trữ menu ID để đảm bảo nhất quán
    private ?int $menuId = null;

    /**
     * Sử dụng template tùy chỉnh
     */
    protected static string $view = 'filament.admin.resources.menu-item-resource.pages.manage-menu-items';

    public function mount(): void
    {
        // Get menu ID from route parameter or URL
        $menuId = request()->route('menu');
        
        // If not found in route, try to extract from URL path
        if (!$menuId) {
            $path = request()->path();
            if (preg_match('/admin\/menu-items\/(\d+)/', $path, $matches)) {
                $menuId = $matches[1];
            }
        }
        
        // If still not found, try URL segments
        if (!$menuId) {
            $segments = request()->segments();
            $menuIndex = array_search('menu-items', $segments);
            if ($menuIndex !== false && isset($segments[$menuIndex + 1])) {
                $menuId = $segments[$menuIndex + 1];
            }
        }
        
        // If still not found, try query parameter
        if (!$menuId) {
            $menuId = request()->get('menu');
        }
        
        // Set menu ID if found
        if ($menuId) {
            $this->menuId = (int) $menuId;
            Log::info('Menu ID set from mount method', ['menu_id' => $this->menuId]);
        } else {
            // If no menu ID found, try to get from session or default to first menu
            $this->menuId = session('current_menu_id');
            if (!$this->menuId) {
                $firstMenu = Menu::first();
                if ($firstMenu) {
                    $this->menuId = $firstMenu->id;
                    session(['current_menu_id' => $this->menuId]);
                }
            }
            Log::info('Menu ID set from session/default', ['menu_id' => $this->menuId]);
        }
        
        // Store menu ID in session for consistency
        if ($this->menuId) {
            session(['current_menu_id' => $this->menuId]);
        }
        
        // Ensure menu is loaded
        $this->getMenu();
    }

    public function getHeading(): string
    {
        $menu = $this->getMenu();
        return 'Quản lý menu: ' . $menu->name;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('toggle_reordering')
                ->label(fn() => $this->isMenuReordering ? 'Thoát chế độ sắp xếp' : 'Sắp xếp menu')
                ->icon(fn() => $this->isMenuReordering ? 'heroicon-o-check' : 'heroicon-o-arrows-up-down')
                ->color(fn() => $this->isMenuReordering ? 'success' : 'gray')
                ->action(function () {
                    $this->isMenuReordering = !$this->isMenuReordering;
                })
                ->requiresConfirmation(false),
                
            Actions\CreateAction::make()
                ->modalHeading('Tạo mục menu mới')
                ->modalWidth('md')
                ->mutateFormDataUsing(function (array $data): array {
                    $data['menu_id'] = $this->getMenu()->id;
                    
                    // Xử lý các loại liên kết khác nhau
                    if ($data['type'] !== 'custom') {
                        $data['url'] = null; // Reset URL nếu không phải custom
                    }
                    
                    // Xử lý thứ tự mặc định
                    if (!isset($data['order'])) {
                        // Lấy thứ tự lớn nhất hiện tại và tăng thêm 1
                        $maxOrder = MenuItem::where('menu_id', $this->getMenu()->id)
                            ->max('order') ?? 0;
                        $data['order'] = $maxOrder + 1;
                    }
                    
                    return $data;
                })
                ->using(function (array $data, string $model): Model {
                    return $model::create($data);
                })
                ->successNotificationTitle('Mục menu đã được tạo thành công'),
        ];
    }

    public function getMenu(): Menu
    {
        // Return cached menu if available
        if ($this->menu !== null) {
            return $this->menu;
        }

        // Priority 1: Use stored menu ID (most reliable during Livewire updates)
        if ($this->menuId !== null) {
            $this->menu = Menu::findOrFail($this->menuId);
            return $this->menu;
        }

        // Priority 2: Use session menu ID
        $sessionMenuId = session('current_menu_id');
        if ($sessionMenuId) {
            $this->menuId = (int) $sessionMenuId;
            $this->menu = Menu::findOrFail($this->menuId);
            return $this->menu;
        }

        // Priority 3: Try to get from URL (only for initial page load)
        $menuId = null;

        // Method 1: From route parameter
        $menuId = request()->route('menu');
        
        // Method 2: From URL path segments
        if (!$menuId) {
            $path = request()->path();
            if (preg_match('/admin\/menu-items\/(\d+)/', $path, $matches)) {
                $menuId = $matches[1];
            }
        }

        // Method 3: From URL segments
        if (!$menuId) {
            $segments = request()->segments();
            $menuIndex = array_search('menu-items', $segments);
            if ($menuIndex !== false && isset($segments[$menuIndex + 1])) {
                $menuId = $segments[$menuIndex + 1];
            }
        }

        // Method 4: From query parameter
        if (!$menuId) {
            $menuId = request()->get('menu');
        }

        // Method 5: From record parameter
        if (!$menuId && request()->route('record')) {
            $menuItem = MenuItem::find(request()->route('record'));
            if ($menuItem) {
                $menuId = $menuItem->menu_id;
            }
        }

        // Method 6: Default to first menu if none found
        if (!$menuId) {
            $firstMenu = Menu::first();
            if ($firstMenu) {
                $menuId = $firstMenu->id;
            }
        }

        // Log để debug
        Log::info('Getting menu ID', [
            'stored_menu_id' => $this->menuId,
            'session_menu_id' => session('current_menu_id'),
            'route_menu' => request()->route('menu'),
            'path' => request()->path(),
            'segments' => request()->segments(),
            'query_menu' => request()->get('menu'),
            'final_menu_id' => $menuId,
            'url' => request()->url(),
            'method' => 'getMenu',
            'route_parameters' => request()->route()->parameters()
        ]);

        if (!$menuId) {
            throw new \Exception('No menu found. Please create a menu first.');
        }

        // Lưu trữ menuId để đảm bảo nhất quán
        $this->menuId = (int) $menuId;
        
        // Store in session for consistency
        session(['current_menu_id' => $this->menuId]);

        // Cache the menu object
        $this->menu = Menu::findOrFail($this->menuId);
        
        // Log menu được tìm thấy
        Log::info('Menu found', [
            'menu_id' => $this->menu->id,
            'menu_name' => $this->menu->name,
            'menu_slug' => $this->menu->slug,
            'menu_location' => $this->menu->location,
            'stored_menu_id' => $this->menuId
        ]);
        
        return $this->menu;
    }

    public function getTitle(): string
    {
        return 'Quản lý menu: ' . $this->getMenu()->name;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index', ['menu' => $this->getMenu()->id]);
    }
    
    /**
     * Xử lý khi menu item được kéo thả từ vị trí này sang vị trí khác
     */
    #[On('reorder')]
    public function reorder(array $items): void
    {
        DB::transaction(function () use ($items) {
            foreach ($items as $item) {
                $record = MenuItem::find($item['id']);
                
                if (! $record) {
                    continue;
                }
                
                // Đảm bảo record thuộc đúng menu
                if ($record->menu_id != $this->getMenu()->id) {
                    continue;
                }
                
                $record->order = $item['order'];
                
                if (array_key_exists('parent_id', $item)) {
                    // Kiểm tra parent_id và chuyển đổi 0 thành NULL
                    $parentId = $item['parent_id'];
                    if ($parentId === 0 || $parentId === '0') {
                        $parentId = null;
                    }
                    
                    // Kiểm tra parent_id có thuộc menu hiện tại không
                    if ($parentId) {
                        $parent = MenuItem::find($parentId);
                        if (!$parent || $parent->menu_id != $this->getMenu()->id) {
                            continue;
                        }
                    }
                    
                    $record->parent_id = $parentId;
                }
                
                $record->save();
            }
        });
        
        Notification::make()
            ->title('Cập nhật thành công')
            ->body('Thứ tự menu đã được cập nhật.')
            ->success()
            ->send();
    }
    
    /**
     * Kiểm tra và đảm bảo menu ID nhất quán
     */
    private function ensureConsistentMenuId(): void
    {
        $currentMenuId = $this->getMenu()->id;
        
        if ($this->menuId === null) {
            $this->menuId = $currentMenuId;
            Log::info('Menu ID initialized', ['menu_id' => $this->menuId]);
        } elseif ($this->menuId !== $currentMenuId) {
            Log::warning('Menu ID inconsistency detected', [
                'stored_menu_id' => $this->menuId,
                'current_menu_id' => $currentMenuId
            ]);
            // Sử dụng menuId đã lưu trữ
            $this->menu = Menu::findOrFail($this->menuId);
        }
    }

    /**
     * Lấy dữ liệu cho view
     */
    #[Computed]
    public function getViewData(): array
    {
        // Đảm bảo menu ID nhất quán
        $this->ensureConsistentMenuId();
        
        // Use refresh counter to force recomputation
        $this->refreshCounter;

        $menuId = $this->menuId ?? $this->getMenu()->id;

        // Lấy locale từ session (được set bởi language-switcher)
        $currentLocale = session('locale', config('app.locale'));

        // Debug current locale
        Log::info('Menu getViewData called', [
            'locale' => $currentLocale,
            'app_locale' => app()->getLocale(),
            'session_locale' => session('locale'),
            'menu_id' => $menuId,
            'refresh_counter' => $this->refreshCounter
        ]);

        // Lấy danh sách menu items hiện tại với cấu trúc đa cấp
        $menuItems = MenuItem::where('menu_id', $menuId)
            ->orderBy('order')
            ->get()
            ->map(function ($item) use ($currentLocale) {
                // Refresh menu item từ database để đảm bảo dữ liệu mới nhất
                $freshItem = MenuItem::find($item->id);
                if (!$freshItem) {
                    return $item;
                }

                // Cập nhật các thuộc tính từ database
                $item->title = $freshItem->title;
                $item->url = $freshItem->url;
                $item->order = $freshItem->order;
                $item->parent_id = $freshItem->parent_id;
                $item->status = $freshItem->status;
                $item->type = $freshItem->type;
                $item->model_type = $freshItem->model_type;
                $item->model_id = $freshItem->model_id;

                // Nếu menu item có liên kết với model, lấy title theo ngôn ngữ
                if ($freshItem->model_type && $freshItem->model_id) {
                    try {
                        $model = $freshItem->model_type::find($freshItem->model_id);
                        if ($model) {
                            // Kiểm tra xem model có hỗ trợ đa ngôn ngữ không
                            if (method_exists($model, 'getTranslation')) {
                                // Thử lấy title trước
                                $translatedTitle = null;
                                if (isset($model->title)) {
                                    $translatedTitle = $model->getTranslation('title', $currentLocale, false);
                                }

                                // Nếu không có title, thử name
                                if (!$translatedTitle && isset($model->name)) {
                                    $translatedTitle = $model->getTranslation('name', $currentLocale, false);
                                }

                                // Cập nhật title nếu có translation
                                if ($translatedTitle) {
                                    $item->title = $translatedTitle;
                                }
                            } else {
                                // Nếu model không hỗ trợ translation, dùng title/name trực tiếp
                                if (isset($model->title)) {
                                    $item->title = $model->title;
                                } elseif (isset($model->name)) {
                                    $item->title = $model->name;
                                }
                            }
                        }
                    } catch (\Exception $e) {
                        // Nếu có lỗi, giữ nguyên title gốc
                        Log::warning('Error getting translation for menu item: ' . $e->getMessage());
                    }
                }
                return $item;
            });

        // Tạo cấu trúc menu đa cấp
        $menuItemsTree = $this->buildMenuTree($menuItems);
            
        // Lấy danh sách trang tĩnh
        $pages = StaticPage::where('status', 'published')
            ->orderBy('title')
            ->get(['id', 'title']);
            
        // Lấy danh sách bài viết
        $posts = Post::where('status', 'published')
            ->orderBy('title')
            ->get(['id', 'title']);
            
        // Lấy tất cả danh mục thay vì chỉ lấy danh mục có trạng thái 'published'
        $categories = Category::orderBy('name')
            ->get(['id', 'name']);

        // Lấy danh sách thương hiệu
        $brands = \App\Models\Brand::orderBy('name')
            ->get(['id', 'name', 'slug']);

        return [
            'menu' => $this->getMenu(),
            'isMenuReordering' => $this->isMenuReordering,
            'menuItems' => $menuItemsTree, // Sử dụng cấu trúc tree thay vì flat list
            'pages' => $pages,
            'posts' => $posts,
            'categories' => $categories,
            'brands' => $brands,
        ];
    }

    /**
     * Xây dựng cấu trúc menu đa cấp (hỗ trợ không giới hạn cấp)
     */
    private function buildMenuTree($menuItems)
    {
        $tree = [];
        $lookup = [];

        // Tạo lookup table và khởi tạo children collection
        foreach ($menuItems as $item) {
            $lookup[$item->id] = $item;
            $item->children = collect();
        }

        // Xây dựng tree đa cấp
        foreach ($menuItems as $item) {
            if ($item->parent_id === null) {
                // Item gốc (cấp 1)
                $tree[] = $item;
            } else {
                // Item con (cấp 2, 3, 4, ...)
                if (isset($lookup[$item->parent_id])) {
                    $lookup[$item->parent_id]->children->push($item);
                }
            }
        }

        // Sắp xếp children theo order cho mỗi cấp
        $this->sortChildrenRecursively($lookup);

        return collect($tree);
    }

    /**
     * Sắp xếp children đệ quy cho tất cả các cấp
     */
    private function sortChildrenRecursively($lookup)
    {
        foreach ($lookup as $item) {
            if ($item->children->count() > 0) {
                // Sắp xếp children theo order
                $item->children = $item->children->sortBy('order');

                // Đệ quy sắp xếp children của children
                foreach ($item->children as $child) {
                    if ($child->children->count() > 0) {
                        $this->sortChildrenRecursively([$child->id => $child]);
                    }
                }
            }
        }
    }
    
    /**
     * Tạo menu item từ trang tĩnh
     */
    public function createPageMenuItems(): void
    {
        if (empty($this->selectedPages)) {
            Notification::make()
                ->title('Chưa chọn trang')
                ->body('Vui lòng chọn ít nhất một trang để thêm vào menu.')
                ->warning()
                ->send();
            return;
        }
        
        $menuId = $this->menuId ?? $this->getMenu()->id;
        $maxOrder = MenuItem::where('menu_id', $menuId)->max('order') ?? 0;
        
        $count = 0;
        foreach ($this->selectedPages as $pageId) {
            $page = StaticPage::find($pageId);
            if (!$page) continue;
            
            MenuItem::create([
                'menu_id' => $menuId,
                'title' => $page->title,
                'type' => 'page',
                'model_type' => StaticPage::class,
                'model_id' => $page->id,
                'order' => $maxOrder + $count + 1,
                'status' => true,
                'target' => '_self',
            ]);
            
            $count++;
        }
        
        $this->selectedPages = [];
        
        Notification::make()
            ->title('Thêm trang thành công')
            ->body("Đã thêm $count trang vào menu.")
            ->success()
            ->send();
        
        $this->clearMenuCache();
        $this->dispatch('menu-items-updated');
        $this->forceReloadMenuItems();
    }
    
    /**
     * Tạo menu item từ bài viết
     */
    public function createPostMenuItems(): void
    {
        if (empty($this->selectedPosts)) {
            Notification::make()
                ->title('Chưa chọn bài viết')
                ->body('Vui lòng chọn ít nhất một bài viết để thêm vào menu.')
                ->warning()
                ->send();
            return;
        }
        
        $menuId = $this->menuId ?? $this->getMenu()->id;
        $maxOrder = MenuItem::where('menu_id', $menuId)->max('order') ?? 0;
        
        $count = 0;
        foreach ($this->selectedPosts as $postId) {
            $post = Post::find($postId);
            if (!$post) continue;
            
            MenuItem::create([
                'menu_id' => $menuId,
                'title' => $post->title,
                'type' => 'post',
                'model_type' => Post::class,
                'model_id' => $post->id,
                'order' => $maxOrder + $count + 1,
                'status' => true,
                'target' => '_self',
            ]);
            
            $count++;
        }
        
        $this->selectedPosts = [];
        
        Notification::make()
            ->title('Thêm bài viết thành công')
            ->body("Đã thêm $count bài viết vào menu.")
            ->success()
            ->send();
        
        $this->clearMenuCache();
        $this->dispatch('menu-items-updated');
        $this->forceReloadMenuItems();
    }
    
    /**
     * Tạo menu item từ danh mục
     */
    public function createCategoryMenuItems(): void
    {
        if (empty($this->selectedCategories)) {
            Notification::make()
                ->title('Chưa chọn danh mục')
                ->body('Vui lòng chọn ít nhất một danh mục để thêm vào menu.')
                ->warning()
                ->send();
            return;
        }
        
        $menuId = $this->menuId ?? $this->getMenu()->id;
        $maxOrder = MenuItem::where('menu_id', $menuId)->max('order') ?? 0;
        
        $count = 0;
        foreach ($this->selectedCategories as $categoryId) {
            $category = Category::find($categoryId);
            if (!$category) continue;
            
            MenuItem::create([
                'menu_id' => $menuId,
                'title' => $category->name,
                'type' => 'category',
                'model_type' => Category::class,
                'model_id' => $category->id,
                'order' => $maxOrder + $count + 1,
                'status' => true,
                'target' => '_self',
            ]);
            
            $count++;
        }
        
        $this->selectedCategories = [];
        
        Notification::make()
            ->title('Thêm danh mục thành công')
            ->body("Đã thêm $count danh mục vào menu.")
            ->success()
            ->send();
        
        $this->clearMenuCache();
        $this->dispatch('menu-items-updated');
        $this->forceReloadMenuItems();
    }
    
    /**
     * Tạo menu item từ URL tùy chỉnh
     */
    public function createCustomUrlMenuItem(): void
    {
        if (empty($this->customUrl) || empty($this->customTitle)) {
            Notification::make()
                ->title('Thiếu thông tin')
                ->body('Vui lòng nhập đầy đủ tiêu đề và URL.')
                ->warning()
                ->send();
            return;
        }
        
        $menuId = $this->menuId ?? $this->getMenu()->id;
        $maxOrder = MenuItem::where('menu_id', $menuId)->max('order') ?? 0;
        
        MenuItem::create([
            'menu_id' => $menuId,
            'title' => $this->customTitle,
            'type' => 'custom',
            'url' => $this->customUrl,
            'order' => $maxOrder + 1,
            'status' => true,
            'target' => '_self',
        ]);
        
        $this->customUrl = null;
        $this->customTitle = null;
        
        Notification::make()
            ->title('Thêm URL thành công')
            ->body('Đã thêm URL tùy chỉnh vào menu.')
            ->success()
            ->send();
        
        $this->clearMenuCache();
        $this->dispatch('menu-items-updated');
        $this->forceReloadMenuItems();
    }
    
    private function refreshTable()
    {
        // Redirect to the same page to refresh
        return redirect()->to(MenuItemResource::getUrl('index', ['menu' => $this->getMenu()->id]));
    }

    /**
     * Xử lý cập nhật cấu trúc menu từ kéo thả JavaScript (hỗ trợ đa cấp)
     */
    public function updateMenuStructure($items)
    {
        try {
            \Log::info('=== UPDATE MENU STRUCTURE ===');
            \Log::info('Items to process:', $items);

            DB::beginTransaction();

            // Hàm đệ quy để xử lý cấu trúc menu đa cấp
            $this->processMenuItems($items);

            DB::commit();

            // Xóa cache menu để áp dụng thay đổi ngay lập tức
            $menu = Menu::find($this->getMenu()->id);
            if ($menu) {
                Cache::forget('menu_' . $menu->slug . '_model');
                Cache::forget('menu_' . $menu->slug . '_items');
                Cache::forget('menu_' . $menu->location);
            }

            // Refresh computed properties để cập nhật view
            $this->refreshCounter++;

            // Thông báo thành công
            Notification::make()
                ->title('Menu đã được cập nhật')
                ->body('Thứ tự và cấu trúc menu đa cấp đã được lưu')
                ->success()
                ->send();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();

            // Ghi log lỗi
            Log::error('Lỗi khi cập nhật cấu trúc menu: ' . $e->getMessage());

            // Thông báo lỗi
            Notification::make()
                ->title('Lỗi khi cập nhật menu')
                ->body('Đã xảy ra lỗi: ' . $e->getMessage())
                ->danger()
                ->send();

            return false;
        }
    }

    /**
     * Xử lý menu items đệ quy để hỗ trợ đa cấp
     */
    private function processMenuItems($items, $parentId = null)
    {
        \Log::info('=== PROCESS MENU ITEMS ===');
        \Log::info('Parent ID:', ['parent_id' => $parentId]);
        \Log::info('Items count:', ['count' => count($items)]);

        foreach ($items as $index => $item) {
            \Log::info("Processing item {$index}:", $item);

            if (isset($item['id'])) {
                $menuItem = MenuItem::find($item['id']);
                if ($menuItem && $menuItem->menu_id == $this->getMenu()->id) {
                    // Cập nhật order
                    if (isset($item['order'])) {
                        $menuItem->order = $item['order'];
                    }

                    // Cập nhật parent_id
                    if ($parentId !== null) {
                        $menuItem->parent_id = $parentId;
                    } elseif (isset($item['parent_id'])) {
                        // Xử lý parent_id từ JavaScript
                        if ($item['parent_id'] === 0 || $item['parent_id'] === '0' || empty($item['parent_id'])) {
                            $menuItem->parent_id = null;
                        } else {
                            $menuItem->parent_id = (int) $item['parent_id'];
                        }
                    } else {
                        $menuItem->parent_id = null;
                    }

                    // Lưu thay đổi
                    $menuItem->save();

                    \Log::info("Menu item saved:", [
                        'id' => $menuItem->id,
                        'title' => $menuItem->title,
                        'order' => $menuItem->order,
                        'parent_id' => $menuItem->parent_id
                    ]);

                    // Xử lý children nếu có
                    if (isset($item['children']) && is_array($item['children'])) {
                        \Log::info("Processing children for item {$menuItem->id}:", $item['children']);
                        $this->processMenuItems($item['children'], $menuItem->id);
                    }
                }
            }
        }
    }

    public function editMenuItem($menuItemId)
    {
        $menuItem = MenuItem::find($menuItemId);
        
        if ($menuItem && $menuItem->menu_id == $this->getMenu()->id) {
            // Redirect to edit page
            return redirect()->to(MenuItemResource::getUrl('edit', ['record' => $menuItemId, 'menu' => $this->getMenu()->id]));
        }
        
        Notification::make()
            ->warning()
            ->title('Không tìm thấy mục menu')
            ->body('Mục menu không tồn tại hoặc không thuộc menu này.')
            ->send();
    }
    
    public function deleteMenuItem($menuItemId)
    {
        $menuItem = MenuItem::find($menuItemId);
        
        if ($menuItem && $menuItem->menu_id == $this->getMenu()->id) {
            $menuItem->delete();
            
            Notification::make()
                ->title('Mục menu đã được xóa')
                ->body('Xóa thành công')
                ->success()
                ->send();
            
            $this->clearMenuCache();
            $this->dispatch('menu-items-updated');
        } else {
            Notification::make()
                ->warning()
                ->title('Không tìm thấy mục menu')
                ->body('Mục menu không tồn tại hoặc không thuộc menu này.')
                ->send();
        }
    }

    public function deleteSelectedMenuItems(): void
    {
        if (empty($this->selectedMenuItems)) {
            Notification::make()
                ->warning()
                ->title('Không có mục nào được chọn')
                ->body('Vui lòng chọn ít nhất một mục menu để xóa.')
                ->send();
            return;
        }

        try {
            $count = count($this->selectedMenuItems);
            MenuItem::whereIn('id', $this->selectedMenuItems)->delete();
            
            $this->selectedMenuItems = [];
            
            Notification::make()
                ->success()
                ->title('Xóa thành công')
                ->body("Đã xóa {$count} mục menu.")
                ->send();
                
            $this->clearMenuCache();
            $this->dispatch('menu-items-updated');
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Lỗi khi xóa')
                ->body('Đã xảy ra lỗi khi xóa các mục menu: ' . $e->getMessage())
                ->send();
        }
    }

    public function createBrandMenuItems()
    {
        $menuId = $this->menuId ?? $this->getMenu()->id;
        
        foreach ($this->selectedBrands as $brandId) {
            $brand = \App\Models\Brand::find($brandId);
            if ($brand) {
                MenuItem::create([
                    'menu_id' => $menuId,
                    'title' => $brand->name,
                    'url' => '/brands/' . $brand->slug,
                    'type' => 'brand',
                    'model_type' => \App\Models\Brand::class,
                    'model_id' => $brand->id,
                    'status' => true,
                    'order' => MenuItem::where('menu_id', $menuId)->max('order') + 1,
                ]);
            }
        }

        $this->selectedBrands = [];
        $this->clearMenuCache();

        Notification::make()
            ->title('Thành công')
            ->body('Đã thêm ' . count($this->selectedBrands) . ' thương hiệu vào menu.')
            ->success()
            ->send();
    }

    #[On('reorderMenuItem')]
    public function reorderMenuItem($items)
    {
        try {
            \Log::info('=== REORDER MENU ITEM CALLED ===');
            \Log::info('Items received:', $items);

            $this->updateMenuStructure($items);

            // Force refresh bằng cách reload từ database
            $this->refreshMenuItems();

            Notification::make()
                ->title('Cập nhật thành công')
                ->body('Thứ tự menu đã được cập nhật.')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi cập nhật')
                ->body('Đã xảy ra lỗi: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Force refresh menu items từ database
     */
    private function refreshMenuItems(): void
    {
        // Tăng counter để force recompute
        $this->refreshCounter++;

        // Clear cache để đảm bảo lấy dữ liệu mới nhất
        $this->clearMenuCache();

        // Dispatch event để refresh frontend
        $this->dispatch('menuItemsUpdated');
        
        // Dispatch event để cập nhật UI
        $this->dispatch('update-menu-structure');
    }

    /**
     * Force reload menu items từ database
     */
    public function forceReloadMenuItems(): void
    {
        // Reset cache menu object nhưng giữ nguyên menuId
        $this->menu = null;
        
        // Tăng counter để force recompute
        $this->refreshCounter++;
        
        // Clear tất cả cache
        $this->clearMenuCache();
        
        // Dispatch events
        $this->dispatch('menuItemsUpdated');
        $this->dispatch('update-menu-structure');
        
        Log::info('Menu items force reloaded', [
            'menu_id' => $this->menuId ?? $this->getMenu()->id,
            'refresh_counter' => $this->refreshCounter
        ]);
    }

    /**
     * Force set menu ID từ URL hiện tại
     */
    public function forceSetMenuIdFromUrl(): void
    {
        $path = request()->path();
        $url = request()->url();
        
        // Tìm menu ID từ URL
        $menuId = null;
        
        // Pattern 1: admin/menu-items/{id}
        if (preg_match('/admin\/menu-items\/(\d+)/', $path, $matches)) {
            $menuId = $matches[1];
        }
        
        // Pattern 2: menu-items/{id}
        if (!$menuId && preg_match('/menu-items\/(\d+)/', $path, $matches)) {
            $menuId = $matches[1];
        }
        
        // Pattern 3: từ segments
        if (!$menuId) {
            $segments = request()->segments();
            $menuIndex = array_search('menu-items', $segments);
            if ($menuIndex !== false && isset($segments[$menuIndex + 1])) {
                $menuId = $segments[$menuIndex + 1];
            }
        }
        
        // Pattern 4: từ query parameter
        if (!$menuId) {
            $menuId = request()->get('menu');
        }
        
        if ($menuId) {
            $this->setMenuId((int) $menuId);
            
            Log::info('Menu ID forced from URL', [
                'url' => $url,
                'path' => $path,
                'menu_id' => $this->menuId
            ]);
            
            Notification::make()
                ->title('Menu ID Updated')
                ->body("Menu ID đã được cập nhật từ URL: {$this->menuId}")
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Error')
                ->body("Không thể tìm thấy Menu ID trong URL: {$url}")
                ->danger()
                ->send();
        }
    }

    /**
     * Set menu ID and update session
     */
    private function setMenuId(int $menuId): void
    {
        $this->menuId = $menuId;
        $this->menu = null; // Reset cache
        session(['current_menu_id' => $menuId]);
        
        Log::info('Menu ID set', [
            'menu_id' => $menuId,
            'session_updated' => true
        ]);
    }

    /**
     * Handle menu ID change from external source
     */
    public function setMenuIdFromExternal(int $menuId): void
    {
        $this->setMenuId($menuId);
        
        // Force reload menu items
        $this->forceReloadMenuItems();
        
        Notification::make()
            ->title('Menu Changed')
            ->body("Đã chuyển sang menu ID: {$menuId}")
            ->success()
            ->send();
    }

    /**
     * Debug menu ID để kiểm tra
     */
    public function debugMenuId(): void
    {
        $currentMenu = $this->getMenu();
        
        Log::info('Debug Menu ID', [
            'stored_menu_id' => $this->menuId,
            'session_menu_id' => session('current_menu_id'),
            'current_menu_id' => $currentMenu->id,
            'menu_name' => $currentMenu->name,
            'menu_slug' => $currentMenu->slug,
            'menu_location' => $currentMenu->location,
            'url' => request()->url(),
            'path' => request()->path(),
            'route_menu' => request()->route('menu'),
            'query_menu' => request()->get('menu'),
            'route_parameters' => request()->route()->parameters(),
            'segments' => request()->segments(),
            'is_livewire_request' => request()->hasHeader('X-Livewire'),
            'livewire_component' => request()->get('fingerprint', [])['name'] ?? 'unknown'
        ]);
        
        $debugInfo = "URL: " . request()->url() . 
                    "\nStored Menu ID: {$this->menuId}" .
                    "\nSession Menu ID: " . session('current_menu_id') .
                    "\nCurrent Menu ID: {$currentMenu->id}" .
                    "\nMenu: {$currentMenu->name}" .
                    "\nIs Livewire Request: " . (request()->hasHeader('X-Livewire') ? 'Yes' : 'No');
        
        Notification::make()
            ->title('Debug Info')
            ->body($debugInfo)
            ->info()
            ->send();
    }

    public function saveMenu()
    {
        try {
            // Clear cache để áp dụng thay đổi
            $this->clearMenuCache();

            Notification::make()
                ->title('Menu đã được lưu')
                ->body('Các thay đổi đã được lưu thành công.')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi khi lưu menu')
                ->body('Đã xảy ra lỗi: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    private function clearMenuCache(): void
    {
        $menu = $this->getMenu();
        
        // Xóa tất cả cache liên quan đến menu
        Cache::forget('menu_' . $menu->slug . '_model');
        Cache::forget('menu_' . $menu->slug . '_items');
        Cache::forget('menu_' . $menu->location);
        Cache::forget('menu_' . $menu->location . '_model');
        Cache::forget('menu_' . $menu->location . '_items');
        
        // Xóa cache theo slug
        Cache::forget('menu_' . $menu->slug);
        
        // Log để debug
        Log::info('Menu cache cleared for menu: ' . $menu->name, [
            'menu_id' => $menu->id,
            'menu_slug' => $menu->slug,
            'menu_location' => $menu->location
        ]);
    }

    /**
     * Khởi tạo dữ liệu chỉnh sửa menu item
     */
    public function startEditingMenuItem($menuItemId)
    {
        $menuItem = MenuItem::find($menuItemId);

        if ($menuItem && $menuItem->menu_id == $this->getMenu()->id) {
            $this->editingMenuItem = [
                'id' => $menuItem->id,
                'title' => $menuItem->title,
                'url' => $menuItem->url,
                'navigation_label' => $menuItem->navigation_label,
                'css_classes' => $menuItem->css_classes,
                'target' => $menuItem->target ?? '_self',
                'description' => $menuItem->description,
            ];
        }
    }

    /**
     * Hủy chỉnh sửa menu item
     */
    public function cancelEditingMenuItem()
    {
        $this->editingMenuItem = [];
    }

    /**
     * Cập nhật menu item
     */
    public function updateMenuItem($menuItemId = null)
    {
        try {
            // Nếu không có menuItemId, lấy từ editingMenuItem
            $menuItemId = $menuItemId ?? $this->editingMenuItem['id'] ?? null;

            if (!$menuItemId) {
                throw new \Exception('Không tìm thấy menu item để cập nhật.');
            }

            $menuItem = MenuItem::findOrFail($menuItemId);

            // Đảm bảo menu item thuộc menu hiện tại
            if ($menuItem->menu_id != $this->getMenu()->id) {
                throw new \Exception('Menu item không thuộc về menu này.');
            }

            // Cập nhật thông tin menu item
            $updateData = [];

            if (isset($this->editingMenuItem['title'])) {
                $updateData['title'] = $this->editingMenuItem['title'];
            }

            if (isset($this->editingMenuItem['url']) && $menuItem->type === 'custom') {
                $updateData['url'] = $this->editingMenuItem['url'];
            }

            if (isset($this->editingMenuItem['navigation_label'])) {
                $updateData['navigation_label'] = $this->editingMenuItem['navigation_label'];
            }

            if (isset($this->editingMenuItem['css_classes'])) {
                $updateData['css_classes'] = $this->editingMenuItem['css_classes'];
            }

            if (isset($this->editingMenuItem['target'])) {
                $updateData['target'] = $this->editingMenuItem['target'];
            }

            if (isset($this->editingMenuItem['description'])) {
                $updateData['description'] = $this->editingMenuItem['description'];
            }

            // Chỉ cập nhật nếu có dữ liệu thay đổi
            if (!empty($updateData)) {
                $menuItem->update($updateData);
            }

            // Xóa cache
            $this->clearMenuCache();

            // Reset trạng thái chỉnh sửa
            $this->editingMenuItem = [];

            Notification::make()
                ->title('Cập nhật thành công')
                ->body('Menu item đã được cập nhật.')
                ->success()
                ->send();

            $this->forceReloadMenuItems();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi cập nhật')
                ->body('Đã xảy ra lỗi: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
} 